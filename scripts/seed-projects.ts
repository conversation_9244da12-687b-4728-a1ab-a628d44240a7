import fs from 'fs';
import path from 'path';

// Define the project structure with content blocks
interface ProjectSeed {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  description: string;
  featuredImage: string;
  status: 'PUBLISHED';
  category: string;
  tags: string[];
  clientName: string;
  clientSector: string;
  projectValue: string;
  startDate: string;
  endDate: string;
  publishedAt: string;
  createdAt: string;
  updatedAt: string;
  seoTitle: string;
  seoDescription: string;
  content: string; // TipTap HTML content
  contentBlocks: any[]; // Predefined content blocks
  features: any[];
  timeline: any[];
  technologies: string[];
  gallery: any[];
  stats: any[];
}

const projectSeeds: ProjectSeed[] = [
  // {
  //   id: "1",
  //   title: "Enterprise Data Center Cape Town",
  //   slug: "enterprise-data-center-cape-town",
  //   excerpt: "State-of-the-art Tier III+ data center facility delivering 99.99% uptime for critical business operations across the Western Cape region.",
  //   description: "A comprehensive enterprise data center solution featuring redundant power systems, advanced cooling technology, and multi-layered security measures.",
  //   featuredImage: "/assets/img/project/datacenter-hero.jpg",
  //   status: "PUBLISHED",
  //   category: "data-centers",
  //   tags: ["Data Center", "Infrastructure", "Cloud", "Security", "Enterprise"],
  //   clientName: "Financial Services Consortium",
  //   clientSector: "Financial Services",
  //   projectValue: "R 120,000,000",
  //   startDate: "2023-01-15",
  //   endDate: "2024-12-15",
  //   publishedAt: "2024-01-15T00:00:00.000Z",
  //   createdAt: "2024-01-10T00:00:00.000Z",
  //   updatedAt: "2024-01-15T00:00:00.000Z",
  //   seoTitle: "Enterprise Data Center Cape Town - Tier III+ Facility | MITC",
  //   seoDescription: "Discover our enterprise data center project in Cape Town featuring Tier III+ design, 99.99% uptime guarantee, redundant power systems, and advanced security measures for critical business operations.",
  //   content: `
  //     <h2>Project Overview</h2>
  //     <p>The Enterprise Data Center Cape Town represents a milestone in African infrastructure development, delivering world-class data center services to the financial sector and beyond. This state-of-the-art facility combines cutting-edge technology with sustainable design principles to create a robust foundation for digital transformation.</p>

  //     <h3>Technical Excellence</h3>
  //     <p>Our data center features <strong>Tier III+ design standards</strong> with comprehensive redundancy across all critical systems. The facility incorporates advanced cooling technologies, including hot/cold aisle containment and free cooling economizers, achieving industry-leading Power Usage Effectiveness (PUE) ratings.</p>

  //     <blockquote>
  //       <p>"This facility sets a new benchmark for data center excellence in Africa, combining reliability, efficiency, and sustainability in ways that directly support our clients' digital transformation goals."</p>
  //       <footer>— Chief Technology Officer, MITC</footer>
  //     </blockquote>

  //     <h3>Security & Compliance</h3>
  //     <p>Multi-layered physical security includes:</p>
  //     <ul>
  //       <li>Biometric access controls at multiple checkpoints</li>
  //       <li>24/7 security personnel and surveillance systems</li>
  //       <li>Mantrap entries and visitor escort protocols</li>
  //       <li>Environmental monitoring and alerting systems</li>
  //     </ul>

  //     <h3>Sustainability Focus</h3>
  //     <p>The facility incorporates renewable energy sources and energy-efficient systems, reducing environmental impact while maintaining operational excellence. Our commitment to sustainability extends beyond compliance to genuine environmental stewardship.</p>
  //   `,
  //   contentBlocks: [
  //     {
  //       id: "hero-1",
  //       type: "hero",
  //       order: 0,
  //       data: {
  //         title: "Enterprise Data Center Cape Town",
  //         subtitle: "Tier III+ Infrastructure Excellence",
  //         description: "State-of-the-art data center facility delivering 99.99% uptime guarantee with advanced cooling, redundant power systems, and multi-layered security for critical business operations.",
  //         backgroundImage: "/assets/img/project/datacenter-hero.jpg",
  //         ctaText: "View Technical Specifications",
  //         ctaLink: "#specifications"
  //       }
  //     },
  //     {
  //       id: "stats-1",
  //       type: "stats",
  //       order: 1,
  //       data: {
  //         title: "Project Impact",
  //         layout: "horizontal",
  //         stats: [
  //           {
  //             number: "99.99%",
  //             label: "Uptime Guarantee",
  //             description: "Tier III+ reliability standards"
  //           },
  //           {
  //             number: "200+",
  //             label: "Server Racks",
  //             description: "High-density computing capacity"
  //           },
  //           {
  //             number: "2MW",
  //             label: "IT Load Capacity",
  //             description: "Scalable power infrastructure"
  //           },
  //           {
  //             number: "5,000m²",
  //             label: "Facility Size",
  //             description: "Purpose-built data center space"
  //           }
  //         ]
  //       }
  //     },
  //     {
  //       id: "features-1",
  //       type: "features",
  //       order: 2,
  //       data: {
  //         title: "Core Infrastructure Features",
  //         layout: "grid",
  //         features: [
  //           {
  //             icon: "fas fa-bolt",
  //             title: "Redundant Power Systems",
  //             description: "N+1 UPS configuration with diesel generators and automatic transfer switches ensuring continuous power supply during any outage scenario."
  //           },
  //           {
  //             icon: "fas fa-snowflake",
  //             title: "Advanced Cooling Technology",
  //             description: "Precision air conditioning with hot/cold aisle containment and free cooling economizers for optimal energy efficiency."
  //           },
  //           {
  //             icon: "fas fa-network-wired",
  //             title: "Multi-Carrier Connectivity",
  //             description: "Multiple fiber providers with diverse routing ensuring high-speed, redundant internet connectivity and carrier neutrality."
  //           },
  //           {
  //             icon: "fas fa-shield-alt",
  //             title: "Multi-Layer Security",
  //             description: "Biometric access controls, 24/7 surveillance, mantrap entries, and comprehensive visitor management systems."
  //           },
  //           {
  //             icon: "fas fa-chart-line",
  //             title: "Real-time Monitoring",
  //             description: "Advanced DCIM systems providing real-time visibility into power, cooling, environmental conditions, and capacity utilization."
  //           },
  //           {
  //             icon: "fas fa-leaf",
  //             title: "Sustainable Design",
  //             description: "Energy-efficient systems, renewable energy integration, and green building practices reducing environmental impact."
  //           }
  //         ]
  //       }
  //     },
  //     {
  //       id: "gallery-1",
  //       type: "gallery",
  //       order: 3,
  //       data: {
  //         columns: 3,
  //         images: [
  //           {
  //             src: "/assets/img/project/datacenter-server-room.jpg",
  //             alt: "Main server room with hot/cold aisle containment",
  //             caption: "High-density server racks with optimized airflow management"
  //           },
  //           {
  //             src: "/assets/img/project/datacenter-power-room.jpg",
  //             alt: "UPS and power distribution systems",
  //             caption: "Redundant power infrastructure with N+1 configuration"
  //           },
  //           {
  //             src: "/assets/img/project/datacenter-cooling.jpg",
  //             alt: "Precision cooling systems",
  //             caption: "Advanced cooling technology with free cooling economizers"
  //           },
  //           {
  //             src: "/assets/img/project/datacenter-noc.jpg",
  //             alt: "Network Operations Center",
  //             caption: "24/7 monitoring and management center"
  //           },
  //           {
  //             src: "/assets/img/project/datacenter-security.jpg",
  //             alt: "Security checkpoint with biometric access",
  //             caption: "Multi-layer physical security systems"
  //           },
  //           {
  //             src: "/assets/img/project/datacenter-exterior.jpg",
  //             alt: "Data center facility exterior",
  //             caption: "Purpose-built facility with sustainable design elements"
  //           }
  //         ]
  //       }
  //     },
  //     {
  //       id: "timeline-1",
  //       type: "timeline",
  //       order: 4,
  //       data: {
  //         title: "Project Timeline",
  //         phases: [
  //           {
  //             phase: "Phase 1",
  //             duration: "8 months",
  //             title: "Design & Planning",
  //             description: "Comprehensive architectural design, engineering specifications, regulatory approvals, and environmental impact assessments.",
  //             status: "completed"
  //           },
  //           {
  //             phase: "Phase 2",
  //             duration: "18 months",
  //             title: "Construction & Infrastructure",
  //             description: "Facility construction, power infrastructure installation, cooling system deployment, and network backbone implementation.",
  //             status: "completed"
  //           },
  //           {
  //             phase: "Phase 3",
  //             duration: "6 months",
  //             title: "Equipment & Testing",
  //             description: "Server rack installation, network equipment deployment, comprehensive testing, and certification processes.",
  //             status: "completed"
  //           },
  //           {
  //             phase: "Phase 4",
  //             duration: "3 months",
  //             title: "Operations & Handover",
  //             description: "Staff training, operational procedures implementation, client onboarding, and full service commencement.",
  //             status: "current"
  //           }
  //         ]
  //       }
  //     },
  //     {
  //       id: "cta-1",
  //       type: "cta",
  //       order: 5,
  //       data: {
  //         title: "Ready to Transform Your Infrastructure?",
  //         description: "Discover how our enterprise data center solutions can support your organization's digital transformation journey with unmatched reliability and performance.",
  //         primaryButton: {
  //           text: "Schedule Consultation",
  //           link: "/contact"
  //         },
  //         secondaryButton: {
  //           text: "Download Specifications",
  //           link: "/assets/downloads/datacenter-specs.pdf"
  //         },
  //         backgroundColor: "#1e40af"
  //       }
  //     }
  //   ],
  //   features: [
  //     {
  //       id: "1",
  //       number: "01",
  //       title: "Redundant Power Systems",
  //       description: "N+1 UPS configuration with diesel generators and automatic transfer switches ensuring continuous power supply.",
  //       order: 0
  //     },
  //     {
  //       id: "2",
  //       number: "02",
  //       title: "Advanced Cooling",
  //       description: "Precision air conditioning with hot/cold aisle containment and free cooling economizers for energy efficiency.",
  //       order: 1
  //     },
  //     {
  //       id: "3",
  //       number: "03",
  //       title: "Network Connectivity",
  //       description: "Multiple fiber providers with diverse routing ensuring high-speed, redundant internet connectivity.",
  //       order: 2
  //     },
  //     {
  //       id: "4",
  //       number: "04",
  //       title: "Physical Security",
  //       description: "Multi-layered security with biometric access, surveillance systems, and 24/7 security personnel.",
  //       order: 3
  //     }
  //   ],
  //   timeline: [
  //     {
  //       id: "1",
  //       phase: "Phase 1",
  //       duration: "8 months",
  //       title: "Design & Planning",
  //       description: "Architectural design, engineering specifications, and regulatory approvals.",
  //       order: 0
  //     },
  //     {
  //       id: "2",
  //       phase: "Phase 2",
  //       duration: "18 months",
  //       title: "Construction & Build-out",
  //       description: "Facility construction, power infrastructure, and cooling system installation.",
  //       order: 1
  //     },
  //     {
  //       id: "3",
  //       phase: "Phase 3",
  //       duration: "6 months",
  //       title: "Equipment Installation",
  //       description: "Server racks, network equipment, and monitoring system deployment.",
  //       order: 2
  //     }
  //   ],
  //   technologies: [
  //     "Schneider Electric UPS Systems",
  //     "Liebert Precision Cooling",
  //     "Cisco Network Infrastructure",
  //     "APC Power Distribution Units",
  //     "Vertiv Environmental Monitoring",
  //     "Honeywell Security Systems"
  //   ],
  //   gallery: [
  //     {
  //       id: "1",
  //       src: "/assets/img/project/datacenter-server-room.jpg",
  //       alt: "Server Room",
  //       caption: "Main server room with hot/cold aisle containment",
  //       order: 0
  //     },
  //     {
  //       id: "2",
  //       src: "/assets/img/project/datacenter-power-room.jpg",
  //       alt: "Power Infrastructure",
  //       caption: "UPS and power distribution systems",
  //       order: 1
  //     },
  //     {
  //       id: "3",
  //       src: "/assets/img/project/datacenter-cooling.jpg",
  //       alt: "Cooling Systems",
  //       caption: "Precision cooling with advanced airflow management",
  //       order: 2
  //     }
  //   ],
  //   stats: [
  //     {
  //       number: "99.99%",
  //       label: "Uptime Guarantee"
  //     },
  //     {
  //       number: "200+",
  //       label: "Server Racks"
  //     },
  //     {
  //       number: "2MW",
  //       label: "IT Load Capacity"
  //     },
  //     {
  //       number: "5,000m²",
  //       label: "Facility Size"
  //     }
  //   ]
  // },
  {
    id: "2",
    title: "Smart City Surveillance Network Johannesburg",
    slug: "smart-city-surveillance-network-johannesburg",
    excerpt: "AI-powered urban surveillance system with 500+ cameras, real-time analytics, and automated threat detection for enhanced public safety.",
    description: "Comprehensive smart city surveillance network leveraging artificial intelligence and machine learning for proactive urban security management.",
    featuredImage: "/assets/img/project/surveillance-hero.jpg",
    status: "PUBLISHED",
    category: "smart-cities",
    tags: ["Smart Cities", "AI", "Security", "Surveillance", "Urban Technology"],
    clientName: "City of Johannesburg Metropolitan Municipality",
    clientSector: "Government & Public Safety",
    projectValue: "R 85,000,000",
    startDate: "2023-03-01",
    endDate: "2024-08-30",
    publishedAt: "2024-02-01T00:00:00.000Z",
    createdAt: "2024-01-20T00:00:00.000Z",
    updatedAt: "2024-02-01T00:00:00.000Z",
    seoTitle: "Smart City Surveillance Network Johannesburg - AI-Powered Urban Security | MITC",
    seoDescription: "Advanced smart city surveillance system with AI analytics, facial recognition, and real-time monitoring for enhanced public safety across Johannesburg's urban areas.",
    content: `
      <h2>Transforming Urban Security</h2>
      <p>The Smart City Surveillance Network represents a paradigm shift in urban security management, combining cutting-edge artificial intelligence with comprehensive video analytics to create a proactive safety ecosystem for Johannesburg's residents and visitors.</p>
      
      <h3>AI-Powered Intelligence</h3>
      <p>Our system employs <strong>advanced machine learning algorithms</strong> for real-time threat detection, behavioral analysis, and predictive security measures. The AI continuously learns and adapts, improving detection accuracy and reducing false positives over time.</p>
      
      <h3>Real-Time Response Capabilities</h3>
      <p>Integration with emergency services enables:</p>
      <ul>
        <li>Automatic incident detection and alerting</li>
        <li>Real-time video streaming to response teams</li>
        <li>GPS-enabled mobile applications for field officers</li>
        <li>Centralized command and control operations</li>
      </ul>
      
      <blockquote>
        <p>"This surveillance network has fundamentally changed how we approach urban security, enabling proactive rather than reactive safety measures across our city."</p>
        <footer>— Director of Public Safety, City of Johannesburg</footer>
      </blockquote>
      
      <h3>Privacy and Compliance</h3>
      <p>The system is designed with privacy protection at its core, incorporating data anonymization, secure storage protocols, and compliance with South African privacy legislation including POPIA (Protection of Personal Information Act).</p>
    `,
    contentBlocks: [
      {
        id: "hero-2",
        type: "hero",
        order: 0,
        data: {
          title: "Smart City Surveillance Network",
          subtitle: "AI-Powered Urban Security",
          description: "Comprehensive surveillance system with 500+ cameras, real-time AI analytics, and automated threat detection transforming public safety across Johannesburg.",
          backgroundImage: "/assets/img/project/surveillance-hero.jpg",
          ctaText: "Explore Technology",
          ctaLink: "#technology"
        }
      },
      {
        id: "stats-2",
        type: "stats",
        order: 1,
        data: {
          title: "Network Coverage",
          layout: "horizontal",
          stats: [
            {
              number: "500+",
              label: "Cameras Deployed",
              description: "Strategic citywide coverage"
            },
            {
              number: "24/7",
              label: "Monitoring",
              description: "Continuous surveillance operations"
            },
            {
              number: "95%",
              label: "Detection Accuracy",
              description: "AI-powered threat identification"
            },
            {
              number: "30sec",
              label: "Response Time",
              description: "Average incident alert speed"
            }
          ]
        }
      },
      {
        id: "features-2",
        type: "features",
        order: 2,
        data: {
          title: "Advanced Surveillance Capabilities",
          layout: "grid",
          features: [
            {
              icon: "fas fa-brain",
              title: "AI-Powered Analytics",
              description: "Advanced machine learning algorithms for automatic threat detection, behavioral analysis, and predictive security measures."
            },
            {
              icon: "fas fa-eye",
              title: "Facial Recognition",
              description: "High-accuracy facial recognition technology for person of interest identification and access control applications."
            },
            {
              icon: "fas fa-mobile-alt",
              title: "Mobile Integration",
              description: "Real-time mobile applications for field officers with live video feeds, incident management, and GPS navigation."
            },
            {
              icon: "fas fa-cloud",
              title: "Cloud Infrastructure",
              description: "Scalable cloud-based storage and processing with edge computing for reduced latency and improved performance."
            },
            {
              icon: "fas fa-shield-alt",
              title: "Privacy Protection",
              description: "Built-in privacy safeguards, data anonymization, and compliance with POPIA and international privacy standards."
            },
            {
              icon: "fas fa-chart-bar",
              title: "Analytics Dashboard",
              description: "Comprehensive reporting and analytics dashboard for crime pattern analysis and operational insights."
            }
          ]
        }
      },
      {
        id: "image-1",
        type: "image",
        order: 3,
        data: {
          src: "/assets/img/project/surveillance-control-room.jpg",
          alt: "Central monitoring and control center",
          caption: "State-of-the-art command center with 24/7 monitoring capabilities",
          alignment: "center"
        }
      },
      {
        id: "timeline-2",
        type: "timeline",
        order: 4,
        data: {
          title: "Implementation Timeline",
          phases: [
            {
              phase: "Phase 1",
              duration: "6 months",
              title: "System Design & Planning",
              description: "Network architecture design, camera placement optimization, AI model development, and infrastructure planning.",
              status: "completed"
            },
            {
              phase: "Phase 2",
              duration: "12 months",
              title: "Installation & Integration",
              description: "Camera installation, network infrastructure deployment, system integration, and initial testing phases.",
              status: "completed"
            },
            {
              phase: "Phase 3",
              duration: "4 months",
              title: "Training & Optimization",
              description: "Staff training, system optimization, AI model fine-tuning, and full operational deployment.",
              status: "current"
            }
          ]
        }
      }
    ],
    features: [
      {
        id: "1",
        number: "01",
        title: "AI-Powered Analytics",
        description: "Advanced machine learning algorithms for automatic threat detection and behavioral analysis.",
        order: 0
      },
      {
        id: "2",
        number: "02",
        title: "Real-time Monitoring",
        description: "24/7 monitoring with instant alerts and automated incident response protocols.",
        order: 1
      },
      {
        id: "3",
        number: "03",
        title: "Mobile Integration",
        description: "Mobile applications for field officers with live video feeds and incident management.",
        order: 2
      }
    ],
    timeline: [
      {
        id: "1",
        phase: "Phase 1",
        duration: "6 months",
        title: "System Design & Planning",
        description: "Network architecture design, camera placement planning, and infrastructure assessment.",
        order: 0
      },
      {
        id: "2",
        phase: "Phase 2",
        duration: "12 months",
        title: "Installation & Deployment",
        description: "Camera installation, network infrastructure deployment, and system integration.",
        order: 1
      }
    ],
    technologies: [
      "AI Video Analytics",
      "Facial Recognition Systems",
      "Cloud Computing Platform",
      "Edge Computing Devices",
      "Mobile Application Development",
      "Network Security Solutions"
    ],
    gallery: [
      {
        id: "1",
        src: "/assets/img/project/surveillance-control-room.jpg",
        alt: "Control Center",
        caption: "Central monitoring and control center",
        order: 0
      },
      {
        id: "2",
        src: "/assets/img/project/surveillance-camera.jpg",
        alt: "Smart Camera",
        caption: "AI-enabled surveillance camera with edge processing",
        order: 1
      }
    ],
    stats: [
      {
        number: "500+",
        label: "Cameras Deployed"
      },
      {
        number: "24/7",
        label: "Monitoring"
      },
      {
        number: "95%",
        label: "Detection Accuracy"
      }
    ]
  },
  {
    id: "3",
    title: "DiGiM Media Broadcasting Platform",
    slug: "digim-media-broadcasting-platform",
    excerpt: "Revolutionary cloud-native broadcasting platform enabling seamless multi-channel content distribution with real-time transcoding and adaptive streaming.",
    description: "Next-generation digital media broadcasting solution transforming content distribution across traditional broadcast, streaming, and mobile platforms.",
    featuredImage: "/assets/img/project/broadcasting-hero.jpg",
    status: "PUBLISHED",
    category: "media-solutions",
    tags: ["Broadcasting", "Media", "Streaming", "Cloud", "Digital Transformation"],
    clientName: "African Broadcasting Network",
    clientSector: "Media & Entertainment",
    location: "Lagos, Nigeria",
    projectValue: "R 65,000,000",
    startDate: "2023-06-01",
    endDate: "2024-11-30",
    publishedAt: "2024-03-01T00:00:00.000Z",
    createdAt: "2024-02-15T00:00:00.000Z",
    updatedAt: "2024-03-01T00:00:00.000Z",
    seoTitle: "DiGiM Media Broadcasting Platform - Revolutionary Digital Content Distribution | MITC",
    seoDescription: "Revolutionary digital media broadcasting platform for seamless multi-channel content distribution with real-time transcoding, adaptive streaming, and cloud-native architecture.",
    content: `
      <h2>Revolutionizing Media Distribution</h2>
      <p>The DiGiM Media Broadcasting Platform represents a quantum leap in digital content distribution technology, enabling broadcasters to seamlessly manage, encode, and distribute content across multiple channels and devices from a single, unified platform.</p>
      
      <h3>Cloud-Native Architecture</h3>
      <p>Built on <strong>modern cloud-native principles</strong>, the platform offers unprecedented scalability, reliability, and performance. Our microservices architecture ensures that each component can scale independently, providing optimal resource utilization and cost efficiency.</p>
      
      <h3>Advanced Content Management</h3>
      <p>The platform features:</p>
      <ul>
        <li>Intuitive content upload and management interface</li>
        <li>Automated workflow orchestration</li>
        <li>Real-time transcoding and encoding</li>
        <li>Adaptive bitrate streaming optimization</li>
        <li>Multi-format content support</li>
      </ul>
      
      <blockquote>
        <p>"DiGiM has transformed our broadcasting capabilities, allowing us to reach audiences across Africa through multiple channels simultaneously while reducing operational complexity."</p>
        <footer>— Chief Technology Officer, African Broadcasting Network</footer>
      </blockquote>
      
      <h3>Global Content Delivery</h3>
      <p>Integrated CDN capabilities ensure optimal content delivery performance across Africa and beyond, with intelligent edge caching and geographic load balancing for the best possible viewer experience.</p>
    `,
    contentBlocks: [
      {
        id: "hero-3",
        type: "hero",
        order: 0,
        data: {
          title: "DiGiM Media Broadcasting Platform",
          subtitle: "Next-Generation Content Distribution",
          description: "Revolutionary cloud-native platform enabling seamless multi-channel content distribution with real-time transcoding, adaptive streaming, and comprehensive content management.",
          backgroundImage: "/assets/img/project/broadcasting-hero.jpg",
          ctaText: "Discover Platform",
          ctaLink: "#platform"
        }
      },
      {
        id: "stats-3",
        type: "stats",
        order: 1,
        data: {
          title: "Platform Performance",
          layout: "horizontal",
          stats: [
            {
              number: "10+",
              label: "Distribution Channels",
              description: "Simultaneous content delivery"
            },
            {
              number: "1M+",
              label: "Concurrent Viewers",
              description: "Peak audience capacity"
            },
            {
              number: "99.9%",
              label: "Platform Uptime",
              description: "Guaranteed availability"
            },
            {
              number: "50+",
              label: "Content Formats",
              description: "Supported media types"
            }
          ]
        }
      },
      {
        id: "features-3",
        type: "features",
        order: 2,
        data: {
          title: "Platform Capabilities",
          layout: "grid",
          features: [
            {
              icon: "fas fa-broadcast-tower",
              title: "Multi-Channel Distribution",
              description: "Simultaneous content distribution across broadcast television, streaming platforms, and mobile applications from a single interface."
            },
            {
              icon: "fas fa-cogs",
              title: "Real-Time Transcoding",
              description: "Automatic video encoding and transcoding for optimal delivery across different devices and network conditions."
            },
            {
              icon: "fas fa-cloud",
              title: "Cloud-Native Architecture",
              description: "Scalable microservices architecture with automatic scaling, load balancing, and fault tolerance capabilities."
            },
            {
              icon: "fas fa-play-circle",
              title: "Adaptive Streaming",
              description: "Dynamic bitrate adjustment based on viewer's network conditions ensuring optimal playback quality."
            },
            {
              icon: "fas fa-chart-line",
              title: "Analytics & Insights",
              description: "Comprehensive viewership analytics, engagement metrics, and performance monitoring dashboards."
            },
            {
              icon: "fas fa-mobile-alt",
              title: "Mobile Optimization",
              description: "Native mobile applications and responsive web interfaces optimized for all device types and screen sizes."
            }
          ]
        }
      },
      {
        id: "gallery-3",
        type: "gallery",
        order: 3,
        data: {
          columns: 2,
          images: [
            {
              src: "/assets/img/project/broadcasting-control-room.jpg",
              alt: "Broadcasting control room",
              caption: "State-of-the-art broadcasting control room with DiGiM platform"
            },
            {
              src: "/assets/img/project/broadcasting-dashboard.jpg",
              alt: "Platform dashboard",
              caption: "Comprehensive content management and analytics dashboard"
            },
            {
              src: "/assets/img/project/broadcasting-mobile.jpg",
              alt: "Mobile application",
              caption: "Native mobile applications for content consumption"
            },
            {
              src: "/assets/img/project/broadcasting-studio.jpg",
              alt: "Production studio",
              caption: "Integrated production studio with live streaming capabilities"
            }
          ]
        }
      },
      {
        id: "cta-3",
        type: "cta",
        order: 4,
        data: {
          title: "Transform Your Broadcasting Capabilities",
          description: "Discover how DiGiM can revolutionize your content distribution strategy with cutting-edge technology and comprehensive platform capabilities.",
          primaryButton: {
            text: "Request Demo",
            link: "/contact"
          },
          secondaryButton: {
            text: "Platform Overview",
            link: "/products/digim"
          },
          backgroundColor: "#8B5CF6"
        }
      }
    ],
    features: [
      {
        id: "1",
        number: "01",
        title: "Multi-Channel Distribution",
        description: "Simultaneous content distribution across broadcast, streaming, and mobile platforms.",
        order: 0
      },
      {
        id: "2",
        number: "02",
        title: "Real-time Transcoding",
        description: "Automatic video encoding and transcoding for optimal delivery across different devices.",
        order: 1
      },
      {
        id: "3",
        number: "03",
        title: "Content Management",
        description: "Comprehensive content management system with scheduling and workflow automation.",
        order: 2
      }
    ],
    timeline: [
      {
        id: "1",
        phase: "Phase 1",
        duration: "4 months",
        title: "Platform Development",
        description: "Core platform development, encoding engine integration, and API development.",
        order: 0
      },
      {
        id: "2",
        phase: "Phase 2",
        duration: "8 months",
        title: "Integration & Testing",
        description: "System integration, performance testing, and user acceptance testing.",
        order: 1
      },
      {
        id: "3",
        phase: "Phase 3",
        duration: "6 months",
        title: "Deployment & Training",
        description: "Production deployment, staff training, and ongoing support setup.",
        order: 2
      }
    ],
    technologies: [
      "Video Encoding & Transcoding",
      "Cloud Computing Platform",
      "CDN Integration",
      "RESTful API Development",
      "Mobile Application Development",
      "Real-time Analytics"
    ],
    gallery: [
      {
        id: "1",
        src: "/assets/img/project/broadcasting-control-room.jpg",
        alt: "Control Room",
        caption: "Broadcasting control room with DiGiM platform",
        order: 0
      },
      {
        id: "2",
        src: "/assets/img/project/broadcasting-dashboard.jpg",
        alt: "Dashboard",
        caption: "Content management and analytics dashboard",
        order: 1
      }
    ],
    stats: [
      {
        number: "10+",
        label: "Distribution Channels"
      },
      {
        number: "1M+",
        label: "Concurrent Viewers"
      },
      {
        number: "99.9%",
        label: "Platform Uptime"
      }
    ]
  }
];

// Create the seed script
const seedProjects = async () => {
  const projectsDir = path.join(process.cwd(), 'src/data/projects');

  // Ensure directory exists
  if (!fs.existsSync(projectsDir)) {
    fs.mkdirSync(projectsDir, { recursive: true });
  }

  // Create project files
  for (const project of projectSeeds) {
    const filePath = path.join(projectsDir, `${project.slug}.json`);
    fs.writeFileSync(filePath, JSON.stringify(project, null, 2));
    console.log(`Created project: ${project.title}`);
  }

  console.log(`\n✅ Successfully seeded ${projectSeeds.length} projects!`);
  console.log('\nProjects created:');
  projectSeeds.forEach((project, index) => {
    console.log(`${index + 1}. ${project.title} (${project.slug})`);
  });
};

// Run the seed script
if (require.main === module) {
  seedProjects().catch(console.error);
}

export { seedProjects, projectSeeds };