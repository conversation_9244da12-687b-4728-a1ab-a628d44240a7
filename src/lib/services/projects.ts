import fs from 'fs';
import path from 'path';

export interface Project {
  id: string;
  title: string;
  slug: string;
  excerpt?: string;
  description?: string;
  content?: string; // TipTap HTML content
  contentBlocks?: any[]; // Predefined content blocks
  featuredImage?: string;
  status: 'DRAFT' | 'PUBLISHED' | 'IN_PROGRESS' | 'COMPLETED' | 'ARCHIVED';
  category?: string;
  tags: string[];
  clientName?: string;
  clientSector?: string;
  projectValue?: string;
  startDate?: string;
  endDate?: string;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
  seoTitle?: string;
  seoDescription?: string;
  features: ProjectFeature[];
  timeline: ProjectTimeline[];
  technologies: string[];
  gallery: ProjectGallery[];
  stats?: ProjectStat[];
}

export interface ProjectFeature {
  id: string;
  number: string;
  title: string;
  description: string;
  order?: number;
}

export interface ProjectTimeline {
  id: string;
  phase: string;
  duration: string;
  title: string;
  description: string;
  order?: number;
}

export interface ProjectGallery {
  id: string;
  src: string;
  alt: string;
  caption?: string;
  order?: number;
}

export interface ProjectStat {
  number: string;
  label: string;
}

export interface ProjectCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  color?: string;
  projectCount: number;
  createdAt: string;
}

export interface ProjectTag {
  id: string;
  name: string;
  slug: string;
  color?: string;
  projectCount: number;
  createdAt: string;
}

const PROJECTS_DIR = path.join(process.cwd(), 'src/data/projects');
const CATEGORIES_FILE = path.join(process.cwd(), 'src/data/project-categories.json');
const TAGS_FILE = path.join(process.cwd(), 'src/data/project-tags.json');

// Ensure directories exist
const ensureDirectoryExists = (dirPath: string) => {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
};

// Initialize data files if they don't exist
const initializeDataFiles = () => {
  ensureDirectoryExists(PROJECTS_DIR);

  if (!fs.existsSync(CATEGORIES_FILE)) {
    const defaultCategories: ProjectCategory[] = [
      {
        id: "1",
        name: "Data Centers",
        slug: "data-centers",
        description: "Enterprise data center infrastructure projects",
        color: "#3B82F6",
        projectCount: 0,
        createdAt: new Date().toISOString(),
      },
      {
        id: "2",
        name: "Smart Cities",
        slug: "smart-cities",
        description: "Urban technology and smart city solutions",
        color: "#10B981",
        projectCount: 0,
        createdAt: new Date().toISOString(),
      },
      {
        id: "3",
        name: "Network Infrastructure",
        slug: "network-infrastructure",
        description: "Network design and implementation projects",
        color: "#F59E0B",
        projectCount: 0,
        createdAt: new Date().toISOString(),
      },
    ];
    fs.writeFileSync(CATEGORIES_FILE, JSON.stringify(defaultCategories, null, 2));
  }

  if (!fs.existsSync(TAGS_FILE)) {
    const defaultTags: ProjectTag[] = [
      {
        id: "1",
        name: "Infrastructure",
        slug: "infrastructure",
        color: "#3B82F6",
        projectCount: 0,
        createdAt: new Date().toISOString(),
      },
      {
        id: "2",
        name: "Cloud",
        slug: "cloud",
        color: "#10B981",
        projectCount: 0,
        createdAt: new Date().toISOString(),
      },
      {
        id: "3",
        name: "Security",
        slug: "security",
        color: "#EF4444",
        projectCount: 0,
        createdAt: new Date().toISOString(),
      },
    ];
    fs.writeFileSync(TAGS_FILE, JSON.stringify(defaultTags, null, 2));
  }
};

// Project CRUD operations
export const getAllProjects = async (): Promise<Project[]> => {
  initializeDataFiles();

  try {
    const files = fs.readdirSync(PROJECTS_DIR).filter(file => file.endsWith('.json'));
    const projects: Project[] = [];

    for (const file of files) {
      const filePath = path.join(PROJECTS_DIR, file);
      const content = fs.readFileSync(filePath, 'utf-8');
      const projectData = JSON.parse(content);

      // Convert legacy format to new format if needed
      const project = convertLegacyProject(projectData, path.basename(file, '.json'));
      projects.push(project);
    }

    return projects.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  } catch (error) {
    console.error('Error reading projects:', error);
    return [];
  }
};

export const getProjectBySlug = async (slug: string): Promise<Project | null> => {
  try {
    const filePath = path.join(PROJECTS_DIR, `${slug}.json`);
    if (!fs.existsSync(filePath)) {
      return null;
    }

    const content = fs.readFileSync(filePath, 'utf-8');
    const projectData = JSON.parse(content);
    return convertLegacyProject(projectData, slug);
  } catch (error) {
    console.error('Error reading project:', error);
    return null;
  }
};

export const getProjectById = async (id: string): Promise<Project | null> => {
  const projects = await getAllProjects();
  return projects.find(p => p.id === id) || null;
};

export const createProject = async (projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>): Promise<Project> => {
  initializeDataFiles();

  const project: Project = {
    ...projectData,
    id: Date.now().toString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  const filePath = path.join(PROJECTS_DIR, `${project.slug}.json`);
  fs.writeFileSync(filePath, JSON.stringify(project, null, 2));

  return project;
};

export const updateProject = async (id: string, projectData: Partial<Project>): Promise<Project | null> => {
  const existingProject = await getProjectById(id);
  if (!existingProject) {
    return null;
  }

  const updatedProject: Project = {
    ...existingProject,
    ...projectData,
    id: existingProject.id, // Ensure ID doesn't change
    createdAt: existingProject.createdAt, // Preserve creation date
    updatedAt: new Date().toISOString(),
  };

  // If slug changed, rename the file
  if (projectData.slug && projectData.slug !== existingProject.slug) {
    const oldFilePath = path.join(PROJECTS_DIR, `${existingProject.slug}.json`);
    const newFilePath = path.join(PROJECTS_DIR, `${projectData.slug}.json`);

    if (fs.existsSync(oldFilePath)) {
      fs.unlinkSync(oldFilePath);
    }

    fs.writeFileSync(newFilePath, JSON.stringify(updatedProject, null, 2));
  } else {
    const filePath = path.join(PROJECTS_DIR, `${updatedProject.slug}.json`);
    fs.writeFileSync(filePath, JSON.stringify(updatedProject, null, 2));
  }

  return updatedProject;
};

export const deleteProject = async (id: string): Promise<boolean> => {
  const project = await getProjectById(id);
  if (!project) {
    return false;
  }

  const filePath = path.join(PROJECTS_DIR, `${project.slug}.json`);
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
    return true;
  }

  return false;
};

// Category CRUD operations
export const getAllCategories = async (): Promise<ProjectCategory[]> => {
  initializeDataFiles();

  try {
    const content = fs.readFileSync(CATEGORIES_FILE, 'utf-8');
    const categories = JSON.parse(content);

    // Update project counts by reading projects directly to avoid recursion
    try {
      const files = fs.readdirSync(PROJECTS_DIR).filter(file => file.endsWith('.json'));
      const projectCategories: string[] = [];

      for (const file of files) {
        const filePath = path.join(PROJECTS_DIR, file);
        const projectContent = fs.readFileSync(filePath, 'utf-8');
        const projectData = JSON.parse(projectContent);
        if (projectData.category) {
          projectCategories.push(projectData.category);
        }
      }

      return categories.map((category: ProjectCategory) => ({
        ...category,
        projectCount: projectCategories.filter(cat => cat === category.slug).length,
      }));
    } catch (projectError) {
      console.error('Error counting projects for categories:', projectError);
      return categories.map((category: ProjectCategory) => ({
        ...category,
        projectCount: 0,
      }));
    }
  } catch (error) {
    console.error('Error reading categories:', error);
    return [];
  }
};

export const createCategory = async (categoryData: Omit<ProjectCategory, 'id' | 'projectCount' | 'createdAt'>): Promise<ProjectCategory> => {
  const categories = await getAllCategories();

  const category: ProjectCategory = {
    ...categoryData,
    id: Date.now().toString(),
    projectCount: 0,
    createdAt: new Date().toISOString(),
  };

  categories.push(category);
  fs.writeFileSync(CATEGORIES_FILE, JSON.stringify(categories, null, 2));

  return category;
};

export const updateCategory = async (id: string, categoryData: Partial<ProjectCategory>): Promise<ProjectCategory | null> => {
  const categories = await getAllCategories();
  const categoryIndex = categories.findIndex(c => c.id === id);

  if (categoryIndex === -1) {
    return null;
  }

  categories[categoryIndex] = {
    ...categories[categoryIndex],
    ...categoryData,
    id: categories[categoryIndex].id, // Preserve ID
    createdAt: categories[categoryIndex].createdAt, // Preserve creation date
  };

  fs.writeFileSync(CATEGORIES_FILE, JSON.stringify(categories, null, 2));
  return categories[categoryIndex];
};

export const deleteCategory = async (id: string): Promise<boolean> => {
  const categories = await getAllCategories();
  const category = categories.find(c => c.id === id);

  if (!category || category.projectCount > 0) {
    return false;
  }

  const filteredCategories = categories.filter(c => c.id !== id);
  fs.writeFileSync(CATEGORIES_FILE, JSON.stringify(filteredCategories, null, 2));

  return true;
};

// Tag CRUD operations
export const getAllTags = async (): Promise<ProjectTag[]> => {
  initializeDataFiles();

  try {
    const content = fs.readFileSync(TAGS_FILE, 'utf-8');
    const tags = JSON.parse(content);

    // Update project counts by reading projects directly to avoid recursion
    try {
      const files = fs.readdirSync(PROJECTS_DIR).filter(file => file.endsWith('.json'));
      const allProjectTags: string[] = [];

      for (const file of files) {
        const filePath = path.join(PROJECTS_DIR, file);
        const projectContent = fs.readFileSync(filePath, 'utf-8');
        const projectData = JSON.parse(projectContent);
        if (projectData.tags && Array.isArray(projectData.tags)) {
          allProjectTags.push(...projectData.tags);
        }
      }

      return tags.map((tag: ProjectTag) => ({
        ...tag,
        projectCount: allProjectTags.filter(t => t === tag.name).length,
      }));
    } catch (projectError) {
      console.error('Error counting projects for tags:', projectError);
      return tags.map((tag: ProjectTag) => ({
        ...tag,
        projectCount: 0,
      }));
    }
  } catch (error) {
    console.error('Error reading tags:', error);
    return [];
  }
};

export const createTag = async (tagData: Omit<ProjectTag, 'id' | 'projectCount' | 'createdAt'>): Promise<ProjectTag> => {
  const tags = await getAllTags();

  const tag: ProjectTag = {
    ...tagData,
    id: Date.now().toString(),
    projectCount: 0,
    createdAt: new Date().toISOString(),
  };

  tags.push(tag);
  fs.writeFileSync(TAGS_FILE, JSON.stringify(tags, null, 2));

  return tag;
};

export const updateTag = async (id: string, tagData: Partial<ProjectTag>): Promise<ProjectTag | null> => {
  const tags = await getAllTags();
  const tagIndex = tags.findIndex(t => t.id === id);

  if (tagIndex === -1) {
    return null;
  }

  tags[tagIndex] = {
    ...tags[tagIndex],
    ...tagData,
    id: tags[tagIndex].id, // Preserve ID
    createdAt: tags[tagIndex].createdAt, // Preserve creation date
  };

  fs.writeFileSync(TAGS_FILE, JSON.stringify(tags, null, 2));
  return tags[tagIndex];
};

export const deleteTag = async (id: string): Promise<boolean> => {
  const tags = await getAllTags();
  const tag = tags.find(t => t.id === id);

  if (!tag || tag.projectCount > 0) {
    return false;
  }

  const filteredTags = tags.filter(t => t.id !== id);
  fs.writeFileSync(TAGS_FILE, JSON.stringify(filteredTags, null, 2));

  return true;
};

// Helper function to convert legacy project format to new format
const convertLegacyProject = (projectData: any, slug: string): Project => {
  // If it's already in the new format, return as is
  if (projectData.id && projectData.createdAt) {
    return projectData;
  }

  // Convert legacy format
  const project: Project = {
    id: Date.now().toString(),
    title: projectData.pageBanner?.pageTitle || projectData.about?.title || 'Untitled Project',
    slug: slug,
    excerpt: projectData.pageBanner?.description || '',
    description: projectData.about?.description || '',
    featuredImage: projectData.about?.image?.src || '',
    status: 'PUBLISHED',
    category: '',
    tags: projectData.technologies || [],
    clientName: projectData.client?.name || '',
    clientSector: projectData.client?.sector || '',
    projectValue: projectData.client?.projectValue || '',
    startDate: '',
    endDate: '',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    seoTitle: projectData.pageBanner?.pageTitle || '',
    seoDescription: projectData.pageBanner?.description || '',
    features: projectData.features?.features?.map((feature: any, index: number) => ({
      id: (index + 1).toString(),
      number: feature.num || String(index + 1).padStart(2, '0'),
      title: feature.title || '',
      description: feature.text || '',
      order: index,
    })) || [],
    timeline: projectData.timeline?.phases?.map((phase: any, index: number) => ({
      id: (index + 1).toString(),
      phase: phase.phase || `Phase ${index + 1}`,
      duration: phase.duration || '',
      title: phase.title || '',
      description: phase.description || '',
      order: index,
    })) || [],
    technologies: projectData.technologies || [],
    gallery: projectData.gallery?.map((item: any, index: number) => ({
      id: (index + 1).toString(),
      src: item.src || '',
      alt: item.alt || '',
      caption: item.caption || '',
      order: index,
    })) || [],
    stats: projectData.about?.stats || [],
  };

  return project;
};