{"id": "2", "title": "Smart City Surveillance Network", "slug": "smart-city-surveillance-network", "excerpt": "Comprehensive urban surveillance system with AI-powered analytics for enhanced public safety and crime prevention.", "description": "Our smart city surveillance network project transforms urban security through intelligent video analytics and real-time monitoring capabilities. The system integrates hundreds of high-definition cameras across strategic locations with advanced AI algorithms for automatic threat detection and incident response.\n\nThe network features facial recognition, license plate recognition, and behavioral analysis capabilities. Real-time alerts are sent to control centers and mobile units, enabling rapid response to security incidents. The system also provides valuable data analytics for urban planning and traffic management.\n\nCloud-based infrastructure ensures scalability and reliability, while edge computing reduces latency for critical real-time applications. The solution includes mobile applications for field officers and web-based dashboards for command center operations.", "featuredImage": "/assets/mitc_images/smartcity/1.jpg", "status": "PUBLISHED", "category": "smart-cities", "tags": ["Smart Cities", "AI", "Security", "Surveillance"], "clientName": "City of Johannesburg", "clientSector": "Government", "projectValue": "R 85 Million", "startDate": "2023-03-01", "endDate": "2024-08-30", "publishedAt": "2024-02-01T00:00:00.000Z", "createdAt": "2024-01-20T00:00:00.000Z", "updatedAt": "2024-02-01T00:00:00.000Z", "seoTitle": "Smart City Surveillance Network - AI-Powered Urban Security", "seoDescription": "Advanced smart city surveillance system with AI analytics, facial recognition, and real-time monitoring for enhanced public safety.", "features": [{"id": "1", "number": "01", "title": "AI-Powered Analytics", "description": "Advanced machine learning algorithms for automatic threat detection and behavioral analysis.", "order": 0}, {"id": "2", "number": "02", "title": "Real-time Monitoring", "description": "24/7 monitoring with instant alerts and automated incident response protocols.", "order": 1}, {"id": "3", "number": "03", "title": "Mobile Integration", "description": "Mobile applications for field officers with live video feeds and incident management.", "order": 2}], "timeline": [{"id": "1", "phase": "Phase 1", "duration": "6 months", "title": "System Design & Planning", "description": "Network architecture design, camera placement planning, and infrastructure assessment.", "order": 0}, {"id": "2", "phase": "Phase 2", "duration": "12 months", "title": "Installation & Deployment", "description": "Camera installation, network infrastructure deployment, and system integration.", "order": 1}], "technologies": ["AI Video Analytics", "Facial Recognition", "Cloud Computing", "Edge Computing", "Mobile Applications"], "gallery": [{"id": "1", "src": "/assets/mitc_images/smartcity/WhatsApp Image 2024-03-26 at 12.33.23.jpeg", "alt": "Control Center", "caption": "Central monitoring and control center", "order": 0}], "stats": [{"number": "500+", "label": "Cameras Deployed"}, {"number": "24/7", "label": "Monitoring"}, {"number": "95%", "label": "Detection Accuracy"}]}