'use client'

import { Component, ReactNode } from 'react'
import { Button } from '@/components/ui/button'
import { AlertTriangle, RefreshCw, Home } from 'lucide-react'
import Link from 'next/link'

interface BlogErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: any
}

interface BlogErrorBoundaryProps {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: any) => void
}

export class BlogErrorBoundary extends Component<BlogErrorBoundaryProps, BlogErrorBoundaryState> {
  constructor(props: BlogErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): BlogErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Blog Error Boundary caught an error:', error, errorInfo)
    this.setState({ errorInfo })
    this.props.onError?.(error, errorInfo)
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <div className="min-h-[400px] flex items-center justify-center p-8">
          <div className="text-center max-w-md">
            <div className="mb-6">
              <AlertTriangle className="h-16 w-16 text-destructive mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-2">Something went wrong</h2>
              <p className="text-muted-foreground">
                We encountered an error while loading the blog content. This might be a temporary issue.
              </p>
            </div>

            <div className="space-y-3">
              <Button onClick={this.handleRetry} className="w-full">
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
              
              <Button variant="outline" asChild className="w-full">
                <Link href="/blog">
                  <Home className="mr-2 h-4 w-4" />
                  Back to Blog
                </Link>
              </Button>
            </div>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-6 text-left">
                <summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground">
                  Error Details (Development)
                </summary>
                <pre className="mt-2 p-4 bg-muted rounded text-xs overflow-auto max-h-40">
                  {this.state.error.toString()}
                  {this.state.errorInfo?.componentStack}
                </pre>
              </details>
            )}
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// Functional error fallback components
export function BlogLoadingError({ 
  onRetry, 
  message = "Failed to load blog content" 
}: { 
  onRetry?: () => void
  message?: string 
}) {
  return (
    <div className="text-center py-12">
      <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
      <h3 className="text-lg font-semibold mb-2">Loading Error</h3>
      <p className="text-muted-foreground mb-4">{message}</p>
      {onRetry && (
        <Button onClick={onRetry} variant="outline">
          <RefreshCw className="mr-2 h-4 w-4" />
          Try Again
        </Button>
      )}
    </div>
  )
}

export function BlogNotFound({ 
  title = "Content Not Found",
  message = "The content you're looking for doesn't exist or has been moved.",
  showBackButton = true 
}: {
  title?: string
  message?: string
  showBackButton?: boolean
}) {
  return (
    <div className="text-center py-16">
      <div className="max-w-md mx-auto">
        <div className="text-6xl font-bold text-muted-foreground mb-4">404</div>
        <h1 className="text-2xl font-bold mb-2">{title}</h1>
        <p className="text-muted-foreground mb-6">{message}</p>
        
        {showBackButton && (
          <div className="space-y-2">
            <Button asChild>
              <Link href="/blog">
                <Home className="mr-2 h-4 w-4" />
                Back to Blog
              </Link>
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

export function BlogEmptyState({
  title = "No Content Available",
  message = "There's no content to display at the moment.",
  action
}: {
  title?: string
  message?: string
  action?: ReactNode
}) {
  return (
    <div className="text-center py-5">
      <div className="mx-auto" style={{maxWidth: '28rem'}}>
        <div className="mb-4">
          <div className="bg-light rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style={{height: '4rem', width: '4rem'}}>
            <AlertTriangle className="text-muted" style={{height: '2rem', width: '2rem'}} />
          </div>
          <h3 className="h5 fw-semibold mb-2">{title}</h3>
          <p className="text-muted">{message}</p>
        </div>

        {action && <div>{action}</div>}
      </div>
    </div>
  )
}

// Network error component
export function BlogNetworkError({ onRetry }: { onRetry?: () => void }) {
  return (
    <div className="text-center py-12">
      <div className="max-w-md mx-auto">
        <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">Connection Error</h3>
        <p className="text-muted-foreground mb-4">
          Unable to connect to the server. Please check your internet connection and try again.
        </p>
        {onRetry && (
          <Button onClick={onRetry}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Retry
          </Button>
        )}
      </div>
    </div>
  )
}

// Blog Error State Component
export function BlogErrorState({
  error,
  onRetry,
  networkError = false
}: {
  error?: string | Error
  onRetry?: () => void
  networkError?: boolean
}) {
  const errorMessage = error instanceof Error ? error.message : error || 'An error occurred'

  return (
    <div className="text-center py-5 px-4">
      <div className="mx-auto" style={{maxWidth: '28rem'}}>
        <div className="mb-4">
          <AlertTriangle className="text-danger mx-auto mb-3" style={{height: '4rem', width: '4rem'}} />
        </div>

        <h3 className="h5 fw-semibold mb-3">
          {networkError ? 'Connection Error' : 'Something went wrong'}
        </h3>
        <p className="text-muted mb-4">
          {networkError
            ? 'Please check your internet connection and try again.'
            : errorMessage
          }
        </p>

        {onRetry && (
          <Button onClick={onRetry} className="d-inline-flex align-items-center">
            <RefreshCw className="me-2" style={{height: '1rem', width: '1rem'}} />
            Try Again
          </Button>
        )}
      </div>
    </div>
  )
}
