import { format } from "date-fns"
import Link from "next/link"
import { BlogPostDetail } from "@/types/blog"
import { BlogCard } from "./blog-card"
import { SocialShare } from "./social-share"
import { TipTapContentRenderer } from "./tiptap-content-renderer"
import { BlockRenderer } from "@/components/pages/blocks/block-renderer"
import { BlogStructuredData, BlogBreadcrumbStructuredData } from "./blog-structured-data"

interface BlogPostDetailProps {
  post: BlogPostDetail
  className?: string
}

export function BlogPostDetailComponent({ post, className }: BlogPostDetailProps) {
  return (
    <>
      {/* Structured Data for SEO */}
      <BlogStructuredData post={post} />
      <BlogBreadcrumbStructuredData post={post} />

      <article className={`blog-post-article ${className || ''}`}>
        {/* Back to Blog */}
        <div className="mb-4">
          <Link href="/blog" className="learn">
            <i className="fa-solid fa-arrow-left"></i>
            <span>Back to Blog</span>
          </Link>
        </div>

        <div className="space30" />

        {/* Article Header */}
        <header className="blog-post-header">
          {/* Category */}
          {post.category && (
            <div className="blog-post-category">
              <span
                className="category-badge"
                style={{
                  backgroundColor: post.category.color || '#03276e',
                  color: '#fff'
                }}
              >
                <i className="fa-solid fa-folder"></i>
                {post.category.name}
              </span>
            </div>
          )}

          <div className="space16" />

          {/* Title */}
          <div className="heading1">
            <h1>{post.title}</h1>
          </div>

          <div className="space16" />

          {/* Excerpt */}
          {post.excerpt && (
            <div className="blog-post-excerpt">
              <p>{post.excerpt}</p>
            </div>
          )}

          <div className="space30" />

          {/* Meta Information */}
          <div className="blog-post-meta">
            <div className="meta-item">
              <img src="/assets/img/icons/user2.png" alt="" />
              <span>By {post.author.name}</span>
            </div>
            <div className="meta-item">
              <img src="/assets/img/icons/date2.png" alt="" />
              <time dateTime={post.publishedAt}>
                {format(new Date(post.publishedAt), "MMMM d, yyyy")}
              </time>
            </div>
            {post.readTime && (
              <div className="meta-item">
                <i className="fa-solid fa-clock"></i>
                <span>{post.readTime} min read</span>
              </div>
            )}
            {post.viewCount && (
              <div className="meta-item">
                <i className="fa-solid fa-eye"></i>
                <span>{post.viewCount.toLocaleString()} views</span>
              </div>
            )}
          </div>

          <div className="space16" />

          {/* Tags */}
          {post.tags && post.tags.length > 0 && (
            <div className="blog-post-tags">
              {post.tags.map((tag) => (
                <span
                  key={tag.slug}
                  className="tag-badge"
                  style={{
                    backgroundColor: tag.color ? `${tag.color}20` : '#f8f9fa',
                    borderColor: tag.color || '#dee2e6',
                    color: tag.color || '#6c757d'
                  }}
                >
                  <i className="fa-solid fa-tag"></i>
                  {tag.name}
                </span>
              ))}
            </div>
          )}

          <div className="space30" />

          {/* Social Share */}
          <div className="blog-post-social">
            <SocialShare
              url={`${process.env.NEXT_PUBLIC_SITE_URL || ''}/blog/${post.slug}`}
              title={post.title}
              description={post.excerpt}
              image={post.featuredImage}
            />
          </div>
        </header>

        {/* Featured Image */}
        {post.featuredImage && (
          <div className="blog-post-featured-image">
            <img
              src={post.featuredImage}
              alt={post.title}
              className="img-fluid"
            />
          </div>
        )}

        <div className="space60" />

        {/* Article Content */}
        <div className="blog-post-content">
          <TipTapContentRenderer
            content={post.content || ''}
          />
        </div>

        <div className="space60" />

        {/* Article Footer */}
        <footer className="blog-post-footer">
          {/* Author Bio */}
          <div className="blog-author-bio">
            <div className="author-bio-content">
              {post.author.image && (
                <div className="author-avatar">
                  <img
                    src={post.author.image}
                    alt={post.author.name || 'Author'}
                  />
                </div>
              )}
              <div className="author-info">
                <h4>About {post.author.name}</h4>
                <p>
                  {post.author.bio || 'Content creator and technology enthusiast sharing insights and experiences in IT consulting, digital transformation, and emerging technologies.'}
                </p>
              </div>
            </div>
          </div>

          <div className="space30" />

          {/* Share Again */}
          <div className="text-center">
            <div className="heading1">
              <h3>Share this article</h3>
            </div>
            <div className="space16" />
            <SocialShare
              url={`${process.env.NEXT_PUBLIC_SITE_URL || ''}/blog/${post.slug}`}
              title={post.title}
              description={post.excerpt}
              image={post.featuredImage}
              variant="large"
            />
          </div>
        </footer>

        <div className="space60" />

        {/* Related Posts */}
        {post.relatedPosts && post.relatedPosts.length > 0 && (
          <section className="blog-related-posts">
            <div className="heading1">
              <h3>Related Articles</h3>
              <div className="space16" />
              <p>Continue reading with these related insights and expert perspectives</p>
            </div>
            <div className="space60" />
            <div className="row">
              {post.relatedPosts.slice(0, 3).map((relatedPost) => (
                <div key={relatedPost.id} className="col-lg-4 col-md-6">
                  <BlogCard
                    post={relatedPost}
                    variant="compact"
                    showExcerpt={false}
                  />
                  <div className="space30" />
                </div>
              ))}
            </div>
          </section>
        )}

        <div className="space60" />

        {/* Navigation to Previous/Next Posts */}
        {(post.previousPost || post.nextPost) && (
          <nav className="blog-post-navigation">
            <div className="row">
              {post.previousPost && (
                <div className="col-md-6">
                  <Link
                    href={`/blog/${post.previousPost.slug}`}
                    className="nav-link prev-post"
                  >
                    <div className="nav-label">
                      <i className="fa-solid fa-arrow-left"></i>
                      Previous Article
                    </div>
                    <div className="nav-title">{post.previousPost.title}</div>
                  </Link>
                </div>
              )}
              {post.nextPost && (
                <div className="col-md-6">
                  <Link
                    href={`/blog/${post.nextPost.slug}`}
                    className="nav-link next-post"
                  >
                    <div className="nav-label">
                      Next Article
                      <i className="fa-solid fa-arrow-right"></i>
                    </div>
                    <div className="nav-title">{post.nextPost.title}</div>
                  </Link>
                </div>
              )}
            </div>
          </nav>
        )}
      </article>
    </>
  )
}

// Enhanced skeleton component for loading state
export function BlogPostDetailSkeleton() {
  return (
    <div className="blog-post-skeleton">
      {/* Back button */}
      <div className="mb-4">
        <div className="skeleton-line" style={{ width: '120px', height: '20px' }}></div>
      </div>

      <div className="space30" />

      {/* Header */}
      <div className="blog-post-header">
        <div className="skeleton-line" style={{ width: '100px', height: '24px' }}></div>
        <div className="space16" />
        <div className="skeleton-line skeleton-line-title"></div>
        <div className="skeleton-line" style={{ width: '75%', height: '32px' }}></div>
        <div className="space16" />
        <div className="skeleton-line" style={{ width: '90%', height: '20px' }}></div>
        <div className="skeleton-line" style={{ width: '80%', height: '20px' }}></div>
        <div className="space30" />
        <div className="d-flex gap-3">
          <div className="skeleton-line" style={{ width: '120px', height: '16px' }}></div>
          <div className="skeleton-line" style={{ width: '100px', height: '16px' }}></div>
          <div className="skeleton-line" style={{ width: '80px', height: '16px' }}></div>
        </div>
        <div className="space16" />
        <div className="d-flex gap-2">
          <div className="skeleton-line" style={{ width: '60px', height: '24px', borderRadius: '12px' }}></div>
          <div className="skeleton-line" style={{ width: '80px', height: '24px', borderRadius: '12px' }}></div>
          <div className="skeleton-line" style={{ width: '70px', height: '24px', borderRadius: '12px' }}></div>
        </div>
      </div>

      {/* Featured image */}
      <div className="mb-8">
        <div className="aspect-[16/9] bg-muted rounded-lg" />
      </div>

      {/* Content */}
      <div className="space-y-4 mb-12">
        {Array.from({ length: 12 }).map((_, i) => (
          <div
            key={i}
            className={`h-4 bg-muted rounded ${
              i % 4 === 3 ? 'w-3/4' : 'w-full'
            }`}
          />
        ))}
      </div>

      {/* Author section */}
      <div className="rounded-lg border bg-muted/50 p-6 mb-8">
        <div className="flex items-start space-x-4">
          <div className="h-16 w-16 bg-muted rounded-full" />
          <div className="flex-1 space-y-2">
            <div className="h-6 w-32 bg-muted rounded" />
            <div className="h-4 bg-muted rounded" />
            <div className="h-4 w-5/6 bg-muted rounded" />
          </div>
        </div>
      </div>

      {/* Related posts */}
      <div className="mt-16">
        <div className="h-8 w-48 bg-muted rounded mb-8" />
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="space-y-4">
              <div className="aspect-[16/9] bg-muted rounded-lg" />
              <div className="space-y-2">
                <div className="h-5 bg-muted rounded" />
                <div className="h-5 w-4/5 bg-muted rounded" />
                <div className="h-4 w-24 bg-muted rounded" />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
