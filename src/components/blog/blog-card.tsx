import Link from "next/link"
import { format } from "date-fns"
import { Clock, User, Calendar, ArrowRight } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { BlogPostCard } from "@/types/blog"
import { cn } from "@/lib/utils"

interface BlogCardProps {
  post: BlogPostCard
  variant?: 'default' | 'featured' | 'compact'
  showExcerpt?: boolean
  showCategory?: boolean
  showTags?: boolean
  showAuthor?: boolean
  showDate?: boolean
  showReadTime?: boolean
  className?: string
}

export function BlogCard({
  post,
  variant = 'default',
  showExcerpt = true,
  showCategory = true,
  showTags = false,
  showAuthor = true,
  showDate = true,
  showReadTime = true,
  className
}: BlogCardProps) {
  const isCompact = variant === 'compact'
  const isFeatured = variant === 'featured'

  return (
    <div className={`blog2-box ${className || ''}`}>
      <div className="image">
        <img
          src={post.featuredImage || "/assets/img/blog/blog2-img1.png"}
          alt={post.title}
          style={{ width: '100%', height: '250px', objectFit: 'cover' }}
        />
      </div>
      <div className="heading5">
        <div className="tags">
          {showAuthor && (
            <Link href="#">
              <img src="/assets/img/icons/user2.png" alt="" /> {post.author.name}
            </Link>
          )}
          {showDate && (
            <Link href="#">
              <img src="/assets/img/icons/date2.png" alt="" />
              {format(new Date(post.publishedAt), "d MMMM yyyy")}
            </Link>
          )}
        </div>
        <h4>
          <Link href={`/blog/${post.slug}`}>{post.title}</Link>
        </h4>
        <div className="space16" />
        {showExcerpt && post.excerpt && (
          <p>{post.excerpt}</p>
        )}
        <div className="space16" />
        <Link href={`/blog/${post.slug}`} className="learn">
          Read More
          <span>
            <i className="fa-solid fa-arrow-right" />
          </span>
        </Link>
      </div>
    </div>
  )
}

// Skeleton component for loading states
export function BlogCardSkeleton() {
  return (
    <div className="blog2-box">
      <div className="image">
        <div className="skeleton-image" style={{ width: '100%', height: '250px', background: '#f0f0f0' }}></div>
      </div>
      <div className="heading5">
        <div className="tags">
          <div className="skeleton-line" style={{ width: '120px', height: '16px' }}></div>
          <div className="skeleton-line" style={{ width: '100px', height: '16px' }}></div>
        </div>
        <div className="skeleton-line skeleton-line-title"></div>
        <div className="space16" />
        <div className="skeleton-line skeleton-line-text"></div>
        <div className="skeleton-line skeleton-line-text-short"></div>
        <div className="space16" />
        <div className="skeleton-line" style={{ width: '80px', height: '20px' }}></div>
      </div>
    </div>
  )
}
