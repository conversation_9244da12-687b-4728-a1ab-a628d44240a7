'use client'

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Blog<PERSON>ard, BlogCardSkeleton } from "./blog-card"
import { BlogFilters } from "./blog-filters"
import { BlogPagination } from "./blog-pagination"
import { BlogSearch } from "./blog-search"
import { BlogErrorBoundary, BlogEmptyState, BlogErrorState } from "./blog-error-boundary"
import { Button } from "@/components/ui/button"
import { BlogPostCard, BlogCategory, BlogTag, BlogPostFilters, BlogPostListResponse } from "@/types/blog"
import { PostStatus } from "@prisma/client"
import { Grid, List, SlidersHorizontal, Wifi, WifiOff } from "lucide-react"
import { cn } from "@/lib/utils"

interface BlogListingProps {
  initialData?: BlogPostListResponse
  categories: BlogCategory[]
  tags: BlogTag[]
  showFilters?: boolean
  showSearch?: boolean
  showViewToggle?: boolean
  itemsPerPage?: number
}

export function BlogListing({
  initialData,
  categories,
  tags,
  showFilters = true,
  showSearch = true,
  showViewToggle = true,
  itemsPerPage = 12
}: BlogListingProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [posts, setPosts] = useState<BlogPostCard[]>(initialData?.posts || [])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [networkError, setNetworkError] = useState(false)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showMobileFilters, setShowMobileFilters] = useState(false)
  const [hasInitialized, setHasInitialized] = useState(!!initialData)
  
  const [pagination, setPagination] = useState({
    currentPage: initialData?.currentPage || 1,
    totalPages: initialData?.totalPages || 1,
    total: initialData?.total || 0,
    hasNextPage: initialData?.hasNextPage || false,
    hasPreviousPage: initialData?.hasPreviousPage || false
  })

  const [filters, setFilters] = useState<BlogPostFilters>({
    search: searchParams.get('search') || '',
    categoryId: searchParams.get('category') || '',
    tagIds: searchParams.get('tags')?.split(',').filter(Boolean) || [],
    status: PostStatus.PUBLISHED
  })

  const fetchPosts = async (page = 1, newFilters = filters) => {
    setLoading(true)
    setError(null)
    setNetworkError(false)

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        perPage: itemsPerPage.toString(),
        status: PostStatus.PUBLISHED,
        ...(newFilters.search && { search: newFilters.search }),
        ...(newFilters.categoryId && { categoryId: newFilters.categoryId }),
        ...(newFilters.tagIds && newFilters.tagIds.length > 0 && { tagIds: newFilters.tagIds.join(',') }),
      })

      const response = await fetch(`/api/blog?${params}`)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (result.success) {
        setPosts(result.data.posts)
        setPagination({
          currentPage: result.data.currentPage,
          totalPages: result.data.totalPages,
          total: result.data.total,
          hasNextPage: result.data.hasNextPage,
          hasPreviousPage: result.data.hasPreviousPage
        })

        // Update URL without causing navigation
        const newParams = new URLSearchParams()
        if (newFilters.search) newParams.set('search', newFilters.search)
        if (newFilters.categoryId) newParams.set('category', newFilters.categoryId)
        if (newFilters.tagIds && newFilters.tagIds.length > 0) newParams.set('tags', newFilters.tagIds.join(','))
        if (page > 1) newParams.set('page', page.toString())

        const newUrl = `${window.location.pathname}${newParams.toString() ? `?${newParams.toString()}` : ''}`
        window.history.replaceState({}, '', newUrl)
      } else {
        setError(result.error || 'Failed to load blog posts')
      }
    } catch (error) {
      console.error('Error fetching posts:', error)
      if (error instanceof TypeError && error.message.includes('fetch')) {
        setNetworkError(true)
      } else {
        setError('Failed to load blog posts. Please try again.')
      }
    } finally {
      setLoading(false)
    }
  }

  const handleFiltersChange = (newFilters: BlogPostFilters) => {
    setFilters(newFilters)
    fetchPosts(1, newFilters)
  }

  const handlePageChange = (page: number) => {
    fetchPosts(page)
    // Scroll to top of blog listing
    document.getElementById('blog-listing')?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSearch = (searchTerm: string) => {
    const newFilters = { ...filters, search: searchTerm }
    handleFiltersChange(newFilters)
  }

  useEffect(() => {
    if (!initialData && !hasInitialized) {
      setHasInitialized(true)
      fetchPosts()
    }
  }, [initialData, hasInitialized])

  // Handle network error or general error
  if (networkError || (error && !loading)) {
    return (
      <BlogErrorBoundary>
        <BlogErrorState
          error={error || 'Network connection failed'}
          onRetry={() => fetchPosts()}
          networkError={networkError}
        />
      </BlogErrorBoundary>
    )
  }

  return (
    <BlogErrorBoundary>
      <div id="blog-listing" className="mb-4">
        {/* Header with Search and View Toggle */}
        <div className="row mb-4">
          {/* Search */}
          {showSearch && (
            <div className="col-lg-6 col-md-8 mb-3 mb-lg-0">
              <BlogSearch
                value={filters.search || ''}
                onChange={handleSearch}
                placeholder="Search blog posts..."
              />
            </div>
          )}

          {/* View Controls */}
          <div className={`${showSearch ? 'col-lg-6 col-md-4' : 'col-12'} d-flex align-items-center justify-content-end`}>
            {/* Mobile Filters Toggle */}
            {showFilters && (
              <div className="d-lg-none me-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowMobileFilters(!showMobileFilters)}
                >
                  <SlidersHorizontal className="me-2" style={{width: '16px', height: '16px'}} />
                  Filters
                </Button>
              </div>
            )}

            {/* View Mode Toggle */}
            {showViewToggle && (
              <div className="d-none d-lg-block me-2">
                <div className="btn-group" role="group">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="px-3"
                  >
                    <Grid style={{width: '16px', height: '16px'}} />
                    <span className="visually-hidden">Grid view</span>
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="px-3"
                  >
                    <List style={{width: '16px', height: '16px'}} />
                    <span className="visually-hidden">List view</span>
                  </Button>
                </div>
              </div>
            )}

            {/* Network Status Indicator */}
            {networkError ? (
              <WifiOff style={{width: '16px', height: '16px'}} className="text-danger" />
            ) : (
              <Wifi style={{width: '16px', height: '16px'}} className="text-muted" />
            )}
          </div>
        </div>

      {/* Filters and Content */}
      <div className="row">
        {/* Sidebar Filters */}
        {showFilters && (
          <div className={`col-lg-3 ${showMobileFilters ? 'd-block' : 'd-none d-lg-block'} mb-4 mb-lg-0`}>
            <BlogFilters
              categories={categories}
              tags={tags}
              selectedFilters={filters}
              onFiltersChange={handleFiltersChange}
            />
          </div>
        )}

        {/* Main Content */}
        <div className={showFilters ? 'col-lg-9' : 'col-12'}>
          {/* Results Count and Status */}
          <div className="d-flex align-items-center justify-content-between mb-4">
            <p className="text-muted mb-0">
              {loading ? (
                <span className="d-flex align-items-center">
                  <div className="spinner-border spinner-border-sm me-2" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                  Loading posts...
                </span>
              ) : error ? (
                <span className="text-danger">Error loading posts</span>
              ) : (
                `Showing ${posts.length} of ${pagination.total} posts`
              )}
            </p>

            {/* Mobile View Toggle */}
            <div className="d-lg-none">
              {showViewToggle && (
                <div className="btn-group" role="group">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="px-2"
                  >
                    <Grid style={{width: '12px', height: '12px'}} />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="px-2"
                  >
                    <List style={{width: '12px', height: '12px'}} />
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Posts Grid/List */}
          {loading ? (
            <div className={viewMode === 'grid' ? 'row g-4' : ''}>
              {Array.from({ length: Math.min(itemsPerPage, 6) }).map((_, index) => (
                <div
                  key={index}
                  className={viewMode === 'grid' ? 'col-lg-4 col-md-6 col-12' : 'mb-4'}
                >
                  <BlogCardSkeleton
                    variant={viewMode === 'list' ? 'compact' : 'default'}
                  />
                </div>
              ))}
            </div>
          ) : posts.length > 0 ? (
            <div className={viewMode === 'grid' ? 'row g-4' : ''}>
              {posts.map((post, index) => (
                <div
                  key={post.id}
                  className={viewMode === 'grid' ? 'col-lg-4 col-md-6 col-12' : 'mb-4'}
                >
                  <BlogCard
                    post={post}
                    variant={
                      viewMode === 'list'
                        ? 'compact'
                        : index === 0 && viewMode === 'grid' && posts.length > 1
                          ? 'featured'
                          : 'default'
                    }
                    showTags={viewMode === 'grid'}
                    className={viewMode === 'list' ? "d-flex flex-column flex-sm-row" : undefined}
                  />
                </div>
              ))}
            </div>
          ) : (
            <BlogEmptyState
              title="No posts found"
              message={
                filters.search || filters.categoryId || (filters.tagIds && filters.tagIds.length > 0)
                  ? "Try adjusting your search criteria or filters."
                  : "No blog posts have been published yet."
              }
              action={
                (filters.search || filters.categoryId || (filters.tagIds && filters.tagIds.length > 0)) ? (
                  <Button
                    variant="outline"
                    onClick={() => handleFiltersChange({
                      search: '',
                      categoryId: '',
                      tagIds: [],
                      status: PostStatus.PUBLISHED
                    })}
                  >
                    Clear filters
                  </Button>
                ) : undefined
              }
            />
          )}

          {/* Pagination */}
          {pagination.totalPages > 1 && !loading && (
            <div className="mt-5">
              <BlogPagination
                currentPage={pagination.currentPage}
                totalPages={pagination.totalPages}
                hasNextPage={pagination.hasNextPage}
                hasPreviousPage={pagination.hasPreviousPage}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </div>
      </div>
    </div>
    </BlogErrorBoundary>
  )
}
