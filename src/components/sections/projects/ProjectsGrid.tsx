"use client";
import Link from 'next/link';
import { useState } from 'react';
import Image from 'next/image';

type Project = {
  id: string;
  pageBanner: {
    pageTitle: string;
    description: string;
  };
  about?: {
    image?: {
      src: string;
      alt?: string;
    };
    stats?: Array<{
      number: string;
      label: string;
    }>;
  };
  client?: {
    name: string;
    sector: string;
    projectValue: string;
  };
  technologies?: string[];
};

type ProjectsGridProps = {
  projects: Project[];
};

export default function ProjectsGrid({ projects }: ProjectsGridProps) {
  const [hoveredProject, setHoveredProject] = useState<string | null>(null);
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());

  const handleImageError = (projectId: string) => {
    setImageErrors(prev => new Set(prev).add(projectId));
  };

  const getProjectImageSrc = (project: Project) => {
    if (imageErrors.has(project.id) || !project.about?.image?.src) {
      return '/assets/img/placeholders/project-placeholder.svg';
    }
    return project.about.image.src;
  };

  return (
    <section className="projects-listing py-5">
      <div className="container">
        <div className="text-center mb-5">
          <span className="subtitle text-success fw-semibold" style={{letterSpacing:2}}>Our Portfolio</span>
          <h1 className="display-5 fw-bold mb-2">Success Stories</h1>
          <div className="decorative-line mx-auto mb-2" style={{width:60, height:4, background:'linear-gradient(90deg,#28a745,#20c997)', borderRadius:2}} />
          <p className="lead text-muted">Discover how we've transformed businesses through innovative technology solutions.</p>
        </div>
        
        <div className="row g-4">
          {projects.map(project => (
            <div className="col-lg-6 col-md-6" key={project.id}>
              <div 
                className={`project-card h-100 position-relative overflow-hidden ${
                  hoveredProject === project.id ? 'hovered' : ''
                }`}
                onMouseEnter={() => setHoveredProject(project.id)}
                onMouseLeave={() => setHoveredProject(null)}
              >
                {/* Background Image */}
                <div className="card-background">
                  <img
                    src={getProjectImageSrc(project)}
                    alt={project.about?.image?.alt || project.pageBanner.pageTitle}
                    className="background-image"
                    onError={() => handleImageError(project.id)}
                    loading="lazy"
                  />
                  <div className="background-overlay" />
                </div>

                {/* Content */}
                <div className="card-content position-absolute w-100 h-100 d-flex flex-column justify-content-between p-4">
                  <div className="project-header">
                    {project.client && (
                      <div className="client-badge mb-3">
                        <span className="badge bg-success bg-opacity-90 text-white px-3 py-2 rounded-pill">
                          {project.client.sector}
                        </span>
                      </div>
                    )}
                    <h3 className="project-title text-white fw-bold mb-3">
                      {project.pageBanner.pageTitle}
                    </h3>
                    <p className="project-description text-white-75 mb-3">
                      {project.pageBanner.description}
                    </p>
                  </div>

                  {/* Stats or Technologies Preview */}
                  <div className="project-details">
                    {project.about?.stats && (
                      <div className="stats-preview mb-3">
                        <div className="row g-2">
                          {project.about.stats.slice(0, 2).map((stat, idx) => (
                            <div key={idx} className="col-6">
                              <div className="stat-item text-center">
                                <div className="stat-number text-white fw-bold">{stat.number}</div>
                                <div className="stat-label text-white-75 small">{stat.label}</div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    {project.client && (
                      <div className="client-info mb-3">
                        <div className="client-details text-white-75 small">
                          <div><strong>Client:</strong> {project.client.name}</div>
                          <div><strong>Value:</strong> {project.client.projectValue}</div>
                        </div>
                      </div>
                    )}
                    
                    <Link 
                      href={`/project/${project.id}`} 
                      className="project-cta-btn d-inline-flex align-items-center"
                    >
                      <span>View Case Study</span>
                      <i className="fas fa-arrow-right ms-2"></i>
                    </Link>
                  </div>
                </div>

                {/* Hover Effect Overlay */}
                <div className="hover-overlay position-absolute w-100 h-100 top-0 start-0" />
              </div>
            </div>
          ))}
        </div>
      </div>

      <style jsx>{`
        .projects-listing {
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        
        .decorative-line {
          margin-bottom: 1rem;
        }
        
        .project-card {
          border-radius: 20px;
          cursor: pointer;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
          overflow: hidden;
          min-height: 400px;
        }
        
        .project-card:hover {
          transform: translateY(-10px) scale(1.02);
          box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        }
        
        .card-background {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 1;
        }
        
        .background-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.4s ease;
        }

        .background-image[src*="placeholder"] {
          object-fit: contain;
          background-color: #f8f9fa;
          padding: 2rem;
        }
        
        .project-card:hover .background-image {
          transform: scale(1.1);
        }
        
        .background-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            135deg,
            rgba(40, 167, 69, 0.8) 0%,
            rgba(32, 201, 151, 0.6) 50%,
            rgba(0, 0, 0, 0.7) 100%
          );
          transition: opacity 0.3s ease;
        }
        
        .project-card:hover .background-overlay {
          opacity: 0.9;
        }
        
        .card-content {
          z-index: 2;
          transition: transform 0.3s ease;
        }
        
        .project-title {
          font-size: 1.4rem;
          line-height: 1.3;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .project-description {
          font-size: 0.95rem;
          line-height: 1.5;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
        
        .text-white-75 {
          color: rgba(255, 255, 255, 0.75);
        }
        
        .stats-preview {
          opacity: 0;
          transform: translateY(20px);
          transition: all 0.3s ease 0.1s;
        }
        
        .project-card:hover .stats-preview {
          opacity: 1;
          transform: translateY(0);
        }
        
        .stat-item {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 10px;
          padding: 0.75rem 0.5rem;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .stat-number {
          font-size: 1.1rem;
        }
        
        .client-info {
          opacity: 0;
          transform: translateY(20px);
          transition: all 0.3s ease 0.2s;
        }
        
        .project-card:hover .client-info {
          opacity: 1;
          transform: translateY(0);
        }
        
        .client-details {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 10px;
          padding: 0.75rem;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .project-cta-btn {
          background: linear-gradient(45deg, #28a745, #20c997);
          color: white;
          padding: 0.75rem 1.5rem;
          border-radius: 25px;
          text-decoration: none;
          font-weight: 600;
          font-size: 0.9rem;
          transition: all 0.3s ease;
          box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .project-cta-btn:hover {
          background: linear-gradient(45deg, #218838, #1e7e34);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
          color: white;
          text-decoration: none;
        }
        
        .project-cta-btn i {
          transition: transform 0.3s ease;
        }
        
        .project-cta-btn:hover i {
          transform: translateX(5px);
        }
        
        .hover-overlay {
          background: linear-gradient(
            45deg,
            rgba(40, 167, 69, 0.1) 0%,
            rgba(32, 201, 151, 0.1) 100%
          );
          opacity: 0;
          transition: opacity 0.3s ease;
          pointer-events: none;
        }
        
        .project-card:hover .hover-overlay {
          opacity: 1;
        }
        
        @media (max-width: 768px) {
          .project-card {
            min-height: 350px;
          }
          
          .project-title {
            font-size: 1.2rem;
          }
          
          .project-description {
            font-size: 0.9rem;
          }
        }
        
        @media (max-width: 576px) {
          .col-lg-6 {
            flex: 0 0 100%;
            max-width: 100%;
          }
        }
      `}</style>
    </section>
  );
}
