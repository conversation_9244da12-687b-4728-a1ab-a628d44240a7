"use client";
import React, { useState } from 'react';
import ProjectImageWithFallback from '@/components/ui/ProjectImageWithFallback';
import ProjectImageWithFallback from '@/components/ui/ProjectImageWithFallback';

interface GalleryProps {
  gallery: Array<{
    src: string;
    alt: string;
  }>;
}

const ProjectGallery: React.FC<GalleryProps> = ({ gallery }) => {
  const [selectedImage, setSelectedImage] = useState<number | null>(null);
  const [imageErrors, setImageErrors] = useState<Set<number>>(new Set());

  const handleImageError = (index: number) => {
    setImageErrors(prev => new Set(prev).add(index));
  };

  const openLightbox = (index: number) => {
    setSelectedImage(index);
  };

  const closeLightbox = () => {
    setSelectedImage(null);
  };

  const nextImage = () => {
    if (selectedImage !== null) {
      setSelectedImage((selectedImage + 1) % gallery.length);
    }
  };

  const prevImage = () => {
    if (selectedImage !== null) {
      setSelectedImage(selectedImage === 0 ? gallery.length - 1 : selectedImage - 1);
    }
  };

  return (
    <section className="project-gallery py-5">
      <div className="container">
        <div className="row">
          <div className="col-lg-8 mx-auto text-center mb-5">
            <span className="subtitle text-primary fw-semibold mb-3 d-block">Project Gallery</span>
            <h2 className="display-6 fw-bold mb-3">Visual Documentation</h2>
            <p className="lead text-muted">Explore the visual journey of our project implementation</p>
          </div>
        </div>
        
        <div className="gallery-grid">
          {gallery.map((image, index) => (
            <div key={index} className="gallery-item" onClick={() => openLightbox(index)}>
              <div className="gallery-card">
                <ProjectImageWithFallback
                  src={image.src}
                  alt={image.alt}
                  className="gallery-image"
                  fallbackSrc="/assets/img/placeholders/project-placeholder-square.svg"
                  onError={() => handleImageError(index)}
                />
                <div className="gallery-overlay">
                  <div className="gallery-icon">
                    <i className="fas fa-expand-alt"></i>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Lightbox */}
      {selectedImage !== null && (
        <div className="lightbox-overlay" onClick={closeLightbox}>
          <div className="lightbox-container">
            <button className="lightbox-close" onClick={closeLightbox}>
              <i className="fas fa-times"></i>
            </button>
            <button className="lightbox-prev" onClick={prevImage}>
              <i className="fas fa-chevron-left"></i>
            </button>
            <button className="lightbox-next" onClick={nextImage}>
              <i className="fas fa-chevron-right"></i>
            </button>
            <ProjectImageWithFallback
              src={gallery[selectedImage].src}
              alt={gallery[selectedImage].alt}
              className="lightbox-image"
              fallbackSrc="/assets/img/placeholders/project-placeholder.svg"
            />
            <div className="lightbox-caption">
              {gallery[selectedImage].alt}
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        .project-gallery {
          background: #f8f9fa;
        }
        
        .subtitle {
          letter-spacing: 2px;
          text-transform: uppercase;
          font-size: 0.9rem;
        }
        
        .gallery-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 2rem;
          margin-top: 2rem;
        }
        
        .gallery-item {
          cursor: pointer;
        }
        
        .gallery-card {
          position: relative;
          border-radius: 1rem;
          overflow: hidden;
          background: white;
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
        }
        
        .gallery-card:hover {
          transform: translateY(-10px);
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .gallery-image {
          width: 100%;
          height: 250px;
          object-fit: cover;
          transition: transform 0.3s ease;
        }
        
        .gallery-card:hover .gallery-image {
          transform: scale(1.1);
        }
        
        .gallery-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 123, 255, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
        }
        
        .gallery-card:hover .gallery-overlay {
          opacity: 1;
        }
        
        .gallery-icon {
          color: white;
          font-size: 2rem;
          transform: scale(0.8);
          transition: transform 0.3s ease;
        }
        
        .gallery-card:hover .gallery-icon {
          transform: scale(1);
        }
        
        .lightbox-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.9);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 9999;
          padding: 2rem;
        }
        
        .lightbox-container {
          position: relative;
          max-width: 90vw;
          max-height: 90vh;
          display: flex;
          flex-direction: column;
          align-items: center;
        }
        
        .lightbox-image {
          max-width: 100%;
          max-height: 80vh;
          object-fit: contain;
          border-radius: 0.5rem;
        }
        
        .lightbox-caption {
          color: white;
          text-align: center;
          margin-top: 1rem;
          font-size: 1.1rem;
        }
        
        .lightbox-close,
        .lightbox-prev,
        .lightbox-next {
          position: absolute;
          background: rgba(255, 255, 255, 0.2);
          border: none;
          color: white;
          width: 3rem;
          height: 3rem;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.2rem;
          cursor: pointer;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);
        }
        
        .lightbox-close:hover,
        .lightbox-prev:hover,
        .lightbox-next:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: scale(1.1);
        }
        
        .lightbox-close {
          top: -1rem;
          right: -1rem;
        }
        
        .lightbox-prev {
          left: -4rem;
          top: 50%;
          transform: translateY(-50%);
        }
        
        .lightbox-next {
          right: -4rem;
          top: 50%;
          transform: translateY(-50%);
        }
        
        @media (max-width: 768px) {
          .gallery-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
          }
          
          .gallery-image {
            height: 200px;
          }
          
          .lightbox-prev,
          .lightbox-next {
            left: 1rem;
            right: 1rem;
            top: auto;
            bottom: 1rem;
            position: fixed;
          }
          
          .lightbox-next {
            right: 1rem;
            left: auto;
          }
          
          .lightbox-close {
            top: 1rem;
            right: 1rem;
            position: fixed;
          }
        }
        
        @media (max-width: 576px) {
          .gallery-grid {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </section>
  );
};

export default ProjectGallery;
