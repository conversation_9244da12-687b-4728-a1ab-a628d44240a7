"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Pagination } from "swiper/modules";
import Link from "next/link";
import "swiper/css";
import "swiper/css/autoplay";
import milestonesData from "@/data/milestones.json";

// Custom styles for enhanced presentation
const customStyles = `
  .milestone-card {
    position: relative;
    overflow: hidden;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid rgba(3, 39, 110, 0.08);
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .milestone-card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 2rem;
  }

  .milestone-card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .milestone-deliverables-container {
    flex: 1;
    margin-top: auto;
  }

  .milestone-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: rgba(3, 39, 110, 0.2);
  }

  .milestone-card.featured {
    background: linear-gradient(135deg, #03276e 0%, #1e40af 100%);
    color: white;
    transform: scale(1.02);
  }

  .milestone-card.featured:hover {
    transform: scale(1.02) translateY(-8px);
    box-shadow: 0 20px 60px rgba(3, 39, 110, 0.3);
  }

  .milestone-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #007bff, #00c6ff);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    transition: all 0.3s ease;
  }

  .milestone-card.featured .milestone-icon {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    color: #03276e;
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
  }

  .milestone-card:hover .milestone-icon {
    transform: scale(1.1) rotate(5deg);
  }

  .milestone-number {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 0.9rem;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
  }

  .milestone-card.featured .milestone-number {
    background: linear-gradient(135deg, #ffd700, #ffb347);
    color: #03276e;
  }

  .milestone-tag {
    display: inline-block;
    padding: 4px 12px;
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 12px;
    border: 1px solid rgba(0, 123, 255, 0.2);
  }

  .milestone-card.featured .milestone-tag {
    background: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.9);
    border-color: rgba(255, 255, 255, 0.3);
  }

  .milestone-deliverables {
    list-style: none;
    padding: 0;
    margin: 16px 0;
  }

  .milestone-deliverables li {
    padding: 6px 0;
    position: relative;
    padding-left: 24px;
    font-size: 0.9rem;
    line-height: 1.5;
    color: #6c757d;
  }

  .milestone-deliverables li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
  }

  .milestone-card.featured .milestone-deliverables li {
    color: rgba(255, 255, 255, 0.8);
  }

  .milestone-card.featured .milestone-deliverables li:before {
    color: #ffd700;
  }

  .milestone-tech-stack {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 12px;
  }

  .tech-badge {
    padding: 2px 8px;
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .milestone-card.featured .tech-badge {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.8);
  }

  .milestones-carousel .swiper-pagination {
    position: relative;
    margin-top: 40px;
    text-align: center;
  }

  .milestones-carousel .swiper-pagination-bullet {
    background: rgba(3, 39, 110, 0.3);
    opacity: 1;
    width: 12px;
    height: 12px;
    margin: 0 6px;
    transition: all 0.3s ease;
  }

  .milestones-carousel .swiper-pagination-bullet-active {
    background: #03276e;
    transform: scale(1.2);
  }

  .milestones-carousel .swiper-slide {
    height: auto;
    display: flex;
  }

  .milestone-item {
    width: 100%;
    height: 100%;
    display: flex;
  }

  .milestones-carousel .swiper-wrapper {
    align-items: stretch;
  }

  .milestones-carousel-container {
    padding: 0 15px;
    position: relative;
  }

  .milestones-carousel {
    padding-bottom: 60px;
  }

  .milestone-card:hover .milestone-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
  }

  .milestone-card.featured:hover .milestone-icon {
    box-shadow: 0 6px 20px rgba(255, 255, 255, 0.3);
  }

  @media (max-width: 992px) {
    .milestone-grid {
      flex-direction: column;
      gap: 20px;
    }

    .milestone-card-content {
      padding: 1.5rem;
    }

    .milestone-deliverables li {
      font-size: 0.85rem;
    }
  }

  @media (max-width: 768px) {
    .milestone-card-content {
      padding: 1.25rem;
    }

    .milestone-icon {
      width: 50px;
      height: 50px;
      margin-bottom: 16px;
    }

    .milestone-number {
      width: 35px;
      height: 35px;
      font-size: 0.8rem;
      top: -8px;
      right: -8px;
    }

    .milestone-deliverables li {
      font-size: 0.8rem;
      padding: 4px 0;
    }

    .tech-badge {
      font-size: 0.7rem;
      padding: 1px 6px;
    }

    .milestones-carousel .swiper-pagination {
      margin-top: 30px;
    }
  }

  @media (max-width: 576px) {
    .milestone-card-content {
      padding: 1rem;
    }

    .milestone-icon {
      width: 45px;
      height: 45px;
      margin-bottom: 12px;
    }

    .milestone-number {
      width: 30px;
      height: 30px;
      font-size: 0.75rem;
    }

    .milestone-deliverables {
      margin: 12px 0;
    }

    .milestone-deliverables li {
      font-size: 0.75rem;
    }

    .milestones-carousel .swiper-pagination {
      margin-top: 20px;
    }
  }
`;

// Milestone icons mapping
const milestoneIcons = [
    "fas fa-chart-line",      // KPI Framework
    "fas fa-tachometer-alt",  // Dashboard System
    "fas fa-network-wired",   // Intranet Platform
    "fas fa-server",          // Data Center
    "fas fa-users-cog",       // Boardroom Solution
    "fas fa-shield-alt"       // Smart City
];

// Swiper configuration
const swiperOptions = {
    modules: [Autoplay, Pagination],
    slidesPerView: 1,
    spaceBetween: 30,
    autoplay: {
        delay: 5000,
        disableOnInteraction: false,
    },
    loop: true,
    pagination: {
        el: ".swiper-pagination",
        clickable: true,
    },
    breakpoints: {
        768: {
            slidesPerView: 2,
            spaceBetween: 30,
        },
        1024: {
            slidesPerView: 3,
            spaceBetween: 30,
        },
    },
};

export default function Section5() {
    const { milestones } = milestonesData;

    return (
        <>
            {/* Inject custom styles */}
            <style jsx>{customStyles}</style>

            {/*=====MILESTONES AREA START=======*/}
            <section className="project-two" id="milestones">
                <div className="project-two__bottom">
                    <div className="container">
                        <div className="row">
                            <div className="col-lg-8 m-auto text-center">
                                <div className="heading1">
                                    <span className="span" data-aos="zoom-in-left" data-aos-duration={700}>
                                        Digital Victories
                                    </span>
                                    <h2 className="text-anime-style-3">17 Years of Transforming African Infrastructure</h2>
                                    <div className="space16" />
                                    <p data-aos="fade-up" data-aos-duration={800}>
                                        From taxi ranks to corporate boardrooms, from crime hotspots to data sanctuaries - <br />
                                        witness how we've revolutionized Africa's digital landscape, one intelligent solution at a time.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className="space60" />

                        {/* Milestones Carousel */}
                        <div className="milestones-carousel-container" data-aos="fade-up" data-aos-duration={900}>
                            <Swiper {...swiperOptions} className="milestones-carousel">
                                {milestones.map((milestone, index) => (
                                    <SwiperSlide key={index}>
                                        <div className="milestone-item">
                                            <div className={`milestone-card ${index === 2 ? 'featured' : ''}`}>
                                                <div className="milestone-number">
                                                    {index + 1}
                                                </div>

                                                <div className="milestone-card-content">
                                                    <div className="milestone-icon">
                                                        <i className={`${milestoneIcons[index] || 'fas fa-cog'} text-white`} style={{ fontSize: '1.5rem' }}></i>
                                                    </div>

                                                    {milestone.duration && (
                                                        <div className="milestone-tag">
                                                            {milestone.duration}
                                                        </div>
                                                    )}

                                                    <div className="milestone-card-body">
                                                        <h4 className="mb-3" style={{
                                                            fontSize: '1.2rem',
                                                            fontWeight: '600',
                                                            lineHeight: '1.4'
                                                        }}>
                                                            {milestone.title}
                                                        </h4>

                                                        <p className="mb-3" style={{
                                                            fontSize: '0.95rem',
                                                            lineHeight: '1.6',
                                                            color: index === 2 ? 'rgba(255,255,255,0.9)' : '#6c757d'
                                                        }}>
                                                            {milestone.description}
                                                        </p>

                                                        {milestone.deliverables && milestone.deliverables.length > 0 && (
                                                            <div className="milestone-deliverables-container">
                                                                <h6 className="mb-2" style={{
                                                                    fontSize: '0.9rem',
                                                                    fontWeight: '600',
                                                                    color: index === 2 ? 'rgba(255,255,255,0.95)' : '#495057'
                                                                }}>
                                                                    Key Deliverables:
                                                                </h6>
                                                                <ul className="milestone-deliverables">
                                                                    {milestone.deliverables.slice(0, 3).map((deliverable, idx) => (
                                                                        <li key={idx}>{deliverable}</li>
                                                                    ))}
                                                                    {milestone.deliverables.length > 3 && (
                                                                        <li style={{ fontStyle: 'italic' }}>
                                                                            +{milestone.deliverables.length - 3} more deliverables
                                                                        </li>
                                                                    )}
                                                                </ul>
                                                            </div>
                                                        )}

                                                        {milestone.technologies && milestone.technologies.length > 0 && (
                                                            <div className="milestone-tech-stack">
                                                                {milestone.technologies.slice(0, 3).map((tech, idx) => (
                                                                    <span key={idx} className="tech-badge">
                                                                        {tech}
                                                                    </span>
                                                                ))}
                                                                {milestone.technologies.length > 3 && (
                                                                    <span className="tech-badge">
                                                                        +{milestone.technologies.length - 3}
                                                                    </span>
                                                                )}
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </SwiperSlide>
                                ))}
                                <div className="swiper-pagination"></div>
                            </Swiper>
                        </div>

                        {/* Call to Action */}
                        <div className="row mt-5">
                            <div className="col-12 text-center">
                                <div className="space30" />
                                <Link
                                    href="/milestones"
                                    className="theme-btn1"
                                    data-aos="fade-up"
                                    data-aos-duration={1000}
                                >
                                    View All Milestones
                                    <span>
                                        <i className="fa-solid fa-arrow-right" />
                                    </span>
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            {/*=====MILESTONES AREA END=======*/}
        </>
    );
}
