"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plus, Search, MoreHorizontal, Edit, Eye, Trash2, Filter } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import ProjectImageWithFallback from "@/components/ui/ProjectImageWithFallback";

// Mock data - replace with actual API calls
const mockProjects = [
  {
    id: "1",
    title: "Enterprise Data Center Cape Town",
    slug: "data-center-cape-town",
    status: "PUBLISHED",
    category: "Data Centers",
    clientName: "Financial Services Group",
    projectValue: "R 120 Million",
    publishedAt: "2024-01-15",
    createdAt: "2024-01-10",
    featuredImage: "/img/projects/cape-town-datacenter.jpg",
  },
  {
    id: "2",
    title: "Smart City Johannesburg",
    slug: "smart-city-johannesburg",
    status: "IN_PROGRESS",
    category: "Smart Cities",
    clientName: "City of Johannesburg",
    projectValue: "R 250 Million",
    publishedAt: null,
    createdAt: "2024-02-01",
    featuredImage: "/img/projects/smart-city-joburg.jpg",
  },
  {
    id: "3",
    title: "Corporate Network Upgrade",
    slug: "corporate-network-upgrade",
    status: "DRAFT",
    category: "Network Infrastructure",
    clientName: "Mining Corporation",
    projectValue: "R 45 Million",
    publishedAt: null,
    createdAt: "2024-02-15",
    featuredImage: "/img/projects/network-upgrade.jpg",
  },
];

const statusColors = {
  DRAFT: "bg-gray-100 text-gray-800",
  PUBLISHED: "bg-green-100 text-green-800",
  IN_PROGRESS: "bg-blue-100 text-blue-800",
  COMPLETED: "bg-purple-100 text-purple-800",
  ARCHIVED: "bg-red-100 text-red-800",
};

export default function ProjectsPage() {
  const [projects, setProjects] = useState(mockProjects);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const filteredProjects = projects.filter((project) => {
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.clientName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || project.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleDelete = async (id: string) => {
    if (confirm("Are you sure you want to delete this project?")) {
      setLoading(true);
      // TODO: Implement actual delete API call
      setProjects(projects.filter(p => p.id !== id));
      setLoading(false);
    }
  };

  const handleStatusChange = async (id: string, newStatus: string) => {
    setLoading(true);
    // TODO: Implement actual status update API call
    setProjects(projects.map(p =>
      p.id === id ? { ...p, status: newStatus as any } : p
    ));
    setLoading(false);
  };

  const getStatusCounts = () => {
    const counts = {
      all: projects.length,
      DRAFT: projects.filter(p => p.status === "DRAFT").length,
      PUBLISHED: projects.filter(p => p.status === "PUBLISHED").length,
      IN_PROGRESS: projects.filter(p => p.status === "IN_PROGRESS").length,
      COMPLETED: projects.filter(p => p.status === "COMPLETED").length,
      ARCHIVED: projects.filter(p => p.status === "ARCHIVED").length,
    };
    return counts;
  };

  const statusCounts = getStatusCounts();

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Projects</h2>
          <p className="text-muted-foreground">
            Manage your project portfolio and showcase your work
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button asChild>
            <Link href="/admin/projects/new">
              <Plus className="mr-2 h-4 w-4" />
              New Project
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statusCounts.all}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Published</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statusCounts.PUBLISHED}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statusCounts.IN_PROGRESS}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Drafts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statusCounts.DRAFT}</div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={statusFilter} onValueChange={setStatusFilter} className="space-y-4">
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="all">All ({statusCounts.all})</TabsTrigger>
            <TabsTrigger value="PUBLISHED">Published ({statusCounts.PUBLISHED})</TabsTrigger>
            <TabsTrigger value="IN_PROGRESS">In Progress ({statusCounts.IN_PROGRESS})</TabsTrigger>
            <TabsTrigger value="DRAFT">Drafts ({statusCounts.DRAFT})</TabsTrigger>
            <TabsTrigger value="COMPLETED">Completed ({statusCounts.COMPLETED})</TabsTrigger>
            <TabsTrigger value="ARCHIVED">Archived ({statusCounts.ARCHIVED})</TabsTrigger>
          </TabsList>
          <div className="flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search projects..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8 w-[300px]"
              />
            </div>
          </div>
        </div>

        <TabsContent value={statusFilter} className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Projects</CardTitle>
              <CardDescription>
                {filteredProjects.length} project{filteredProjects.length !== 1 ? 's' : ''} found
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Project</TableHead>
                    {/* <TableHead>Client</TableHead> */}
                    <TableHead>Category</TableHead>
                    {/* <TableHead>Value</TableHead> */}
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredProjects.map((project) => (
                    <TableRow key={project.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <div className="h-10 w-10 rounded overflow-hidden bg-gray-100">
                            <ProjectImageWithFallback
                              src={project.featuredImage || ''}
                              alt={project.title}
                              className="h-full w-full"
                              fallbackSrc="/assets/img/placeholders/project-placeholder-small.svg"
                            />
                          </div>
                          <div>
                            <div className="font-medium">{project.title}</div>
                            <div className="text-sm text-muted-foreground">
                              /{project.slug}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      {/* <TableCell>{project.clientName}</TableCell> */}
                      <TableCell>{project.category}</TableCell>
                      {/* <TableCell>{project.projectValue}</TableCell> */}
                      <TableCell>
                        <Badge className={statusColors[project.status as keyof typeof statusColors]}>
                          {project.status.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {new Date(project.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/projects/${project.id}`}>
                                <Eye className="mr-2 h-4 w-4" />
                                View
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/projects/${project.id}/edit`}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleStatusChange(project.id, "PUBLISHED")}
                              disabled={project.status === "PUBLISHED"}
                            >
                              Publish
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleStatusChange(project.id, "DRAFT")}
                              disabled={project.status === "DRAFT"}
                            >
                              Move to Draft
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleStatusChange(project.id, "ARCHIVED")}
                              disabled={project.status === "ARCHIVED"}
                            >
                              Archive
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDelete(project.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}