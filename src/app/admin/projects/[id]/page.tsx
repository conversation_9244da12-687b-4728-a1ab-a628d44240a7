"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  Edit,
  Eye,
  Calendar,
  DollarSign,
  Building,
  Clock,
  ExternalLink
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";

// Mock data - replace with actual API call
const mockProject = {
  id: "1",
  title: "Enterprise Data Center Cape Town",
  slug: "data-center-cape-town",
  excerpt: "State-of-the-art enterprise data center facility with redundant power, cooling, and network infrastructure supporting critical business operations.",
  description: "Our enterprise data center project in Cape Town delivers world-class infrastructure supporting mission-critical applications for major financial institutions and technology companies. The facility features Tier III+ design standards with 99.99% uptime guarantee, redundant power systems, and advanced cooling technologies.\n\nThe 5,000 square meter facility houses over 200 server racks with capacity for 2MW of IT load. Advanced monitoring systems provide real-time visibility into power, cooling, and environmental conditions. The facility incorporates sustainable design principles with energy-efficient cooling systems and renewable energy integration.\n\nSecurity features include biometric access controls, 24/7 surveillance, and multi-layered physical security measures. The data center serves as a regional hub for cloud services, disaster recovery, and high-performance computing applications.",
  featuredImage: "/assets/img/project/project1-img4.png",
  status: "PUBLISHED",
  category: "Data Centers",
  tags: ["Data Center", "Infrastructure", "Cloud", "Security"],
  clientName: "Financial Services Group",
  clientSector: "Financial Services",
  projectValue: "R 120 Million",
  startDate: "2023-01-15",
  endDate: "2024-12-15",
  publishedAt: "2024-01-15",
  createdAt: "2024-01-10",
  seoTitle: "Enterprise Data Center Cape Town - MITC Project",
  seoDescription: "Discover our enterprise data center project in Cape Town featuring Tier III+ design, 99.99% uptime guarantee, and advanced security measures.",
  features: [
    {
      id: "1",
      number: "01",
      title: "Redundant Power Systems",
      description: "N+1 UPS configuration with diesel generators and automatic transfer switches ensuring continuous power supply."
    },
    {
      id: "2",
      number: "02",
      title: "Advanced Cooling",
      description: "Precision air conditioning with hot/cold aisle containment and free cooling economizers for energy efficiency."
    },
    {
      id: "3",
      number: "03",
      title: "Network Connectivity",
      description: "Multiple fiber providers with diverse routing ensuring high-speed, redundant internet connectivity."
    },
    {
      id: "4",
      number: "04",
      title: "Physical Security",
      description: "Multi-layered security with biometric access, surveillance systems, and 24/7 security personnel."
    },
    {
      id: "5",
      number: "05",
      title: "Environmental Monitoring",
      description: "Real-time monitoring of temperature, humidity, power, and environmental conditions with automated alerts."
    }
  ],
  timeline: [
    {
      id: "1",
      phase: "Phase 1",
      duration: "8 months",
      title: "Design & Planning",
      description: "Architectural design, engineering specifications, and regulatory approvals."
    },
    {
      id: "2",
      phase: "Phase 2",
      duration: "18 months",
      title: "Construction & Build-out",
      description: "Facility construction, power infrastructure, and cooling system installation."
    },
    {
      id: "3",
      phase: "Phase 3",
      duration: "6 months",
      title: "Equipment Installation",
      description: "Server racks, network equipment, and monitoring system deployment."
    },
    {
      id: "4",
      phase: "Phase 4",
      duration: "3 months",
      title: "Testing & Commissioning",
      description: "System testing, certification, and operational readiness verification."
    }
  ],
  technologies: [
    "Schneider Electric UPS",
    "Liebert Cooling Systems",
    "Cisco Network Infrastructure",
    "APC Power Distribution",
    "Environmental Monitoring",
    "Fire Suppression Systems",
    "Access Control Systems",
    "DCIM Software"
  ],
  gallery: [
    {
      id: "1",
      src: "/assets/img/project/datacenter-1.jpg",
      alt: "Server Room",
      caption: "Main server room with hot/cold aisle containment"
    },
    {
      id: "2",
      src: "/assets/img/project/datacenter-2.jpg",
      alt: "Power Infrastructure",
      caption: "UPS and power distribution systems"
    },
    {
      id: "3",
      src: "/assets/img/project/datacenter-3.jpg",
      alt: "Cooling Systems",
      caption: "Precision cooling and environmental controls"
    },
    {
      id: "4",
      src: "/assets/img/project/datacenter-4.jpg",
      alt: "Network Operations Center",
      caption: "24/7 monitoring and operations center"
    }
  ],
  stats: [
    {
      number: "99.99%",
      label: "Uptime Guarantee"
    },
    {
      number: "200+",
      label: "Server Racks"
    },
    {
      number: "2MW",
      label: "IT Load Capacity"
    },
    {
      number: "5,000m²",
      label: "Facility Size"
    }
  ]
};

const statusColors = {
  DRAFT: "bg-gray-100 text-gray-800",
  PUBLISHED: "bg-green-100 text-green-800",
  IN_PROGRESS: "bg-blue-100 text-blue-800",
  COMPLETED: "bg-purple-100 text-purple-800",
  ARCHIVED: "bg-red-100 text-red-800",
};

export default function ProjectViewPage() {
  const params = useParams();
  const router = useRouter();
  const [project, setProject] = useState(mockProject);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // TODO: Fetch project data from API
    // fetchProject(params.id);
  }, [params.id]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/projects">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Projects
            </Link>
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <div>
            <div className="flex items-center space-x-2">
              <h2 className="text-3xl font-bold tracking-tight">{project.title}</h2>
              <Badge className={statusColors[project.status as keyof typeof statusColors]}>
                {project.status.replace('_', ' ')}
              </Badge>
            </div>
            <p className="text-muted-foreground">
              Created on {formatDate(project.createdAt)}
              {project.publishedAt && ` • Published on ${formatDate(project.publishedAt)}`}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" asChild>
            <Link href={`/projects/${project.slug}`} target="_blank">
              <ExternalLink className="mr-2 h-4 w-4" />
              View Live
            </Link>
          </Button>
          <Button asChild>
            <Link href={`/admin/projects/${project.id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Project
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2 space-y-6">
          {/* Featured Image */}
          {project.featuredImage && (
            <Card>
              <CardContent className="p-0">
                <div className="relative aspect-video">
                  <Image
                    src={project.featuredImage}
                    alt={project.title}
                    fill
                    className="object-cover rounded-t-lg"
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Description */}
          <Card>
            <CardHeader>
              <CardTitle>Project Description</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                {project.description.split('\n\n').map((paragraph, index) => (
                  <p key={index} className="mb-4 text-sm leading-relaxed">
                    {paragraph}
                  </p>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Features */}
          {project.features.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Project Features</CardTitle>
                <CardDescription>
                  Key features and capabilities of this project
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {project.features.map((feature) => (
                    <div key={feature.id} className="flex space-x-4">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium">
                          {feature.number}
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium mb-1">{feature.title}</h4>
                        <p className="text-sm text-muted-foreground">{feature.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Timeline */}
          {project.timeline.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Project Timeline</CardTitle>
                <CardDescription>
                  Project phases and milestones
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {project.timeline.map((phase, index) => (
                    <div key={phase.id} className="flex space-x-4">
                      <div className="flex-shrink-0 flex flex-col items-center">
                        <div className="w-8 h-8 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-sm font-medium">
                          {index + 1}
                        </div>
                        {index < project.timeline.length - 1 && (
                          <div className="w-px h-12 bg-border mt-2" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="font-medium">{phase.title}</h4>
                          <Badge variant="outline" className="text-xs">
                            {phase.phase}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            <Clock className="w-3 h-3 mr-1" />
                            {phase.duration}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{phase.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Technologies */}
          {project.technologies.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Technologies Used</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {project.technologies.map((tech) => (
                    <Badge key={tech} variant="outline">
                      {tech}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <div className="space-y-6">
          {/* Project Info */}
          <Card>
            <CardHeader>
              <CardTitle>Project Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Building className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Client</p>
                  <p className="text-sm text-muted-foreground">{project.clientName}</p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <div className="h-4 w-4 text-muted-foreground flex items-center justify-center">
                  <div className="w-2 h-2 bg-current rounded-full" />
                </div>
                <div>
                  <p className="text-sm font-medium">Sector</p>
                  <p className="text-sm text-muted-foreground">{project.clientSector}</p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Project Value</p>
                  <p className="text-sm text-muted-foreground">{project.projectValue}</p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Duration</p>
                  <p className="text-sm text-muted-foreground">
                    {formatDate(project.startDate)} - {formatDate(project.endDate)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Project Stats */}
          {project.stats && project.stats.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Project Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {project.stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-2xl font-bold text-primary">{stat.number}</div>
                      <div className="text-sm text-muted-foreground">{stat.label}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Tags */}
          {project.tags.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Tags</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {project.tags.map((tag) => (
                    <Badge key={tag} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* SEO Info */}
          <Card>
            <CardHeader>
              <CardTitle>SEO Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium mb-1">SEO Title</p>
                <p className="text-sm text-muted-foreground">{project.seoTitle || 'Not set'}</p>
              </div>
              <div>
                <p className="text-sm font-medium mb-1">SEO Description</p>
                <p className="text-sm text-muted-foreground">{project.seoDescription || 'Not set'}</p>
              </div>
              <div>
                <p className="text-sm font-medium mb-1">URL Slug</p>
                <p className="text-sm text-muted-foreground">/{project.slug}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}