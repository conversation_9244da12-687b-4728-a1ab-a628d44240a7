"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Save,
  Eye,
  ArrowLeft,
  Plus,
  X,
  Upload,
  Calendar,
  DollarSign,
  Building,
  Tag,
  Image as ImageIcon,
  Trash2
} from "lucide-react";
import Link from "next/link";
import { TipTapEditor } from "@/components/editor/TipTapEditor";
import { BlockRenderer } from "@/components/content-blocks";

// Mock data - replace with actual API call
const mockProject = {
  id: "1",
  title: "Enterprise Data Center Cape Town",
  slug: "data-center-cape-town",
  excerpt: "State-of-the-art enterprise data center facility with redundant power, cooling, and network infrastructure supporting critical business operations.",
  description: "Our enterprise data center project in Cape Town delivers world-class infrastructure supporting mission-critical applications for major financial institutions and technology companies. The facility features Tier III+ design standards with 99.99% uptime guarantee, redundant power systems, and advanced cooling technologies.\n\nThe 5,000 square meter facility houses over 200 server racks with capacity for 2MW of IT load. Advanced monitoring systems provide real-time visibility into power, cooling, and environmental conditions. The facility incorporates sustainable design principles with energy-efficient cooling systems and renewable energy integration.\n\nSecurity features include biometric access controls, 24/7 surveillance, and multi-layered physical security measures. The data center serves as a regional hub for cloud services, disaster recovery, and high-performance computing applications.",
  content: "<h2>Project Overview</h2><p>The Enterprise Data Center Cape Town represents a milestone in African infrastructure development, delivering world-class data center services to the financial sector and beyond.</p><h3>Technical Excellence</h3><p>Our data center features <strong>Tier III+ design standards</strong> with comprehensive redundancy across all critical systems.</p>",
  contentBlocks: [],
  featuredImage: "/assets/img/project/project1-img4.png",
  status: "PUBLISHED",
  category: "data-centers",
  tags: ["Data Center", "Infrastructure", "Cloud", "Security"],
  clientName: "Financial Services Group",
  clientSector: "Financial Services",
  projectValue: "R 120 Million",
  startDate: "2023-01-15",
  endDate: "2024-12-15",
  seoTitle: "Enterprise Data Center Cape Town - MITC Project",
  seoDescription: "Discover our enterprise data center project in Cape Town featuring Tier III+ design, 99.99% uptime guarantee, and advanced security measures.",
  features: [
    {
      id: "1",
      number: "01",
      title: "Redundant Power Systems",
      description: "N+1 UPS configuration with diesel generators and automatic transfer switches ensuring continuous power supply."
    },
    {
      id: "2",
      number: "02",
      title: "Advanced Cooling",
      description: "Precision air conditioning with hot/cold aisle containment and free cooling economizers for energy efficiency."
    }
  ],
  timeline: [
    {
      id: "1",
      phase: "Phase 1",
      duration: "8 months",
      title: "Design & Planning",
      description: "Architectural design, engineering specifications, and regulatory approvals."
    },
    {
      id: "2",
      phase: "Phase 2",
      duration: "18 months",
      title: "Construction & Build-out",
      description: "Facility construction, power infrastructure, and cooling system installation."
    }
  ],
  technologies: [
    "Schneider Electric UPS",
    "Liebert Cooling Systems",
    "Cisco Network Infrastructure",
    "APC Power Distribution"
  ]
};

interface ProjectFeature {
  id: string;
  number: string;
  title: string;
  description: string;
}

interface ProjectTimeline {
  id: string;
  phase: string;
  duration: string;
  title: string;
  description: string;
}

export default function EditProjectPage() {
  const params = useParams();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("basic");

  // Initialize with mock data - replace with API call
  const [title, setTitle] = useState(mockProject.title);
  const [slug, setSlug] = useState(mockProject.slug);
  const [excerpt, setExcerpt] = useState(mockProject.excerpt);
  const [description, setDescription] = useState(mockProject.description);
  const [content, setContent] = useState(mockProject.content || "");
  const [contentBlocks, setContentBlocks] = useState(mockProject.contentBlocks || []);
  const [featuredImage, setFeaturedImage] = useState(mockProject.featuredImage);
  const [status, setStatus] = useState(mockProject.status);
  const [category, setCategory] = useState(mockProject.category);
  const [tags, setTags] = useState<string[]>(mockProject.tags);
  const [newTag, setNewTag] = useState("");
  const [previewMode, setPreviewMode] = useState(false);

  // Project Details
  const [clientName, setClientName] = useState(mockProject.clientName);
  const [clientSector, setClientSector] = useState(mockProject.clientSector);
  const [projectValue, setProjectValue] = useState(mockProject.projectValue);
  const [startDate, setStartDate] = useState(mockProject.startDate);
  const [endDate, setEndDate] = useState(mockProject.endDate);

  // Features
  const [features, setFeatures] = useState<ProjectFeature[]>(mockProject.features);

  // Timeline
  const [timeline, setTimeline] = useState<ProjectTimeline[]>(mockProject.timeline);

  // Technologies
  const [technologies, setTechnologies] = useState<string[]>(mockProject.technologies);
  const [newTechnology, setNewTechnology] = useState("");

  // SEO
  const [seoTitle, setSeoTitle] = useState(mockProject.seoTitle);
  const [seoDescription, setSeoDescription] = useState(mockProject.seoDescription);

  useEffect(() => {
    // TODO: Fetch project data from API
    // fetchProject(params.id);
  }, [params.id]);

  // Generate slug from title
  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const handleTitleChange = (value: string) => {
    setTitle(value);
  };

  const addTag = () => {
    if (newTag && !tags.includes(newTag)) {
      setTags([...tags, newTag]);
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const addTechnology = () => {
    if (newTechnology && !technologies.includes(newTechnology)) {
      setTechnologies([...technologies, newTechnology]);
      setNewTechnology("");
    }
  };

  const removeTechnology = (techToRemove: string) => {
    setTechnologies(technologies.filter(tech => tech !== techToRemove));
  };

  const addFeature = () => {
    const newFeature: ProjectFeature = {
      id: Date.now().toString(),
      number: String(features.length + 1).padStart(2, '0'),
      title: "",
      description: ""
    };
    setFeatures([...features, newFeature]);
  };

  const updateFeature = (id: string, field: keyof ProjectFeature, value: string) => {
    setFeatures(features.map(feature =>
      feature.id === id ? { ...feature, [field]: value } : feature
    ));
  };

  const removeFeature = (id: string) => {
    setFeatures(features.filter(feature => feature.id !== id));
  };

  const addTimelinePhase = () => {
    const newPhase: ProjectTimeline = {
      id: Date.now().toString(),
      phase: `Phase ${timeline.length + 1}`,
      duration: "",
      title: "",
      description: ""
    };
    setTimeline([...timeline, newPhase]);
  };

  const updateTimelinePhase = (id: string, field: keyof ProjectTimeline, value: string) => {
    setTimeline(timeline.map(phase =>
      phase.id === id ? { ...phase, [field]: value } : phase
    ));
  };

  const removeTimelinePhase = (id: string) => {
    setTimeline(timeline.filter(phase => phase.id !== id));
  };

  const handleSave = async (publishNow = false) => {
    setLoading(true);

    const projectData = {
      id: params.id,
      title,
      slug,
      excerpt,
      description,
      featuredImage,
      status: publishNow ? "PUBLISHED" : status,
      category,
      tags,
      clientName,
      clientSector,
      projectValue,
      startDate: startDate ? new Date(startDate) : null,
      endDate: endDate ? new Date(endDate) : null,
      features,
      timeline,
      technologies,
      seoTitle,
      seoDescription,
      publishedAt: publishNow ? new Date() : null,
    };

    try {
      // TODO: Implement actual API call
      console.log("Updating project:", projectData);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      router.push(`/admin/projects/${params.id}`);
    } catch (error) {
      console.error("Error updating project:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (confirm("Are you sure you want to delete this project? This action cannot be undone.")) {
      setLoading(true);
      try {
        // TODO: Implement actual delete API call
        console.log("Deleting project:", params.id);

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        router.push("/admin/projects");
      } catch (error) {
        console.error("Error deleting project:", error);
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/projects/${params.id}`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Project
            </Link>
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Edit Project</h2>
            <p className="text-muted-foreground">
              Update project information and content
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="destructive"
            size="sm"
            onClick={handleDelete}
            disabled={loading}
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
          <Button variant="outline" onClick={() => handleSave(false)} disabled={loading}>
            <Save className="mr-2 h-4 w-4" />
            Save Changes
          </Button>
          <Button onClick={() => handleSave(true)} disabled={loading}>
            <Eye className="mr-2 h-4 w-4" />
            Update & Publish
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
          <TabsTrigger value="blocks">Content Blocks</TabsTrigger>
          <TabsTrigger value="details">Project Details</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
          <TabsTrigger value="seo">SEO</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Update the basic details for your project
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Project Title *</Label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => handleTitleChange(e.target.value)}
                    placeholder="Enter project title"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="slug">URL Slug *</Label>
                  <Input
                    id="slug"
                    value={slug}
                    onChange={(e) => setSlug(e.target.value)}
                    placeholder="project-url-slug"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="excerpt">Project Excerpt</Label>
                <Textarea
                  id="excerpt"
                  value={excerpt}
                  onChange={(e) => setExcerpt(e.target.value)}
                  placeholder="Brief description of the project"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Project Description</Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Detailed project description"
                  rows={6}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select value={status} onValueChange={setStatus}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="DRAFT">Draft</SelectItem>
                      <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                      <SelectItem value="COMPLETED">Completed</SelectItem>
                      <SelectItem value="PUBLISHED">Published</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Select value={category} onValueChange={setCategory}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="data-centers">Data Centers</SelectItem>
                      <SelectItem value="smart-cities">Smart Cities</SelectItem>
                      <SelectItem value="network-infrastructure">Network Infrastructure</SelectItem>
                      <SelectItem value="cloud-solutions">Cloud Solutions</SelectItem>
                      <SelectItem value="cybersecurity">Cybersecurity</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="featuredImage">Featured Image URL</Label>
                <div className="flex space-x-2">
                  <Input
                    id="featuredImage"
                    value={featuredImage}
                    onChange={(e) => setFeaturedImage(e.target.value)}
                    placeholder="https://example.com/image.jpg"
                  />
                  <Button variant="outline" size="icon">
                    <Upload className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Tags</Label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => removeTag(tag)}
                      />
                    </Badge>
                  ))}
                </div>
                <div className="flex space-x-2">
                  <Input
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    placeholder="Add a tag"
                    onKeyPress={(e) => e.key === 'Enter' && addTag()}
                  />
                  <Button type="button" variant="outline" onClick={addTag}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Main Content</CardTitle>
              <CardDescription>
                Write the main content for your project using the rich text editor
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TipTapEditor
                content={content}
                onChange={setContent}
                placeholder="Write your project content here..."
                className="min-h-[400px]"
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="blocks" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Content Blocks</CardTitle>
              <CardDescription>
                Manage predefined content blocks for your project page
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold">Preview Mode</h3>
                    <p className="text-sm text-muted-foreground">
                      Toggle to preview how content blocks will appear on the live site
                    </p>
                  </div>
                  <Button
                    variant={previewMode ? "default" : "outline"}
                    onClick={() => setPreviewMode(!previewMode)}
                  >
                    <Eye className="mr-2 h-4 w-4" />
                    {previewMode ? "Edit Mode" : "Preview Mode"}
                  </Button>
                </div>

                {previewMode ? (
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <h4 className="font-semibold mb-4">Content Blocks Preview</h4>
                    {contentBlocks.length > 0 ? (
                      <BlockRenderer blocks={contentBlocks} />
                    ) : (
                      <p className="text-muted-foreground text-center py-8">
                        No content blocks configured yet. Switch to Edit Mode to add blocks.
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="text-sm text-muted-foreground">
                      Content blocks are managed through the project data structure.
                      This is a preview of the current blocks configuration.
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <pre className="text-xs overflow-auto max-h-96">
                        {JSON.stringify(contentBlocks, null, 2)}
                      </pre>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      To modify content blocks, edit the project JSON file directly or use the API.
                      Future versions will include a visual block editor.
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="details" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Project Details</CardTitle>
              <CardDescription>
                Client information and project specifics
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="clientName">Client Name</Label>
                  <div className="relative">
                    <Building className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="clientName"
                      value={clientName}
                      onChange={(e) => setClientName(e.target.value)}
                      placeholder="Client company name"
                      className="pl-8"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="clientSector">Client Sector</Label>
                  <Input
                    id="clientSector"
                    value={clientSector}
                    onChange={(e) => setClientSector(e.target.value)}
                    placeholder="e.g., Financial Services, Healthcare"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="projectValue">Project Value</Label>
                <div className="relative">
                  <DollarSign className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="projectValue"
                    value={projectValue}
                    onChange={(e) => setProjectValue(e.target.value)}
                    placeholder="e.g., R 120 Million"
                    className="pl-8"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startDate">Start Date</Label>
                  <div className="relative">
                    <Calendar className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="startDate"
                      type="date"
                      value={startDate}
                      onChange={(e) => setStartDate(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endDate">End Date</Label>
                  <div className="relative">
                    <Calendar className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="endDate"
                      type="date"
                      value={endDate}
                      onChange={(e) => setEndDate(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Technologies Used</Label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {technologies.map((tech) => (
                    <Badge key={tech} variant="outline" className="flex items-center gap-1">
                      {tech}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => removeTechnology(tech)}
                      />
                    </Badge>
                  ))}
                </div>
                <div className="flex space-x-2">
                  <Input
                    value={newTechnology}
                    onChange={(e) => setNewTechnology(e.target.value)}
                    placeholder="Add a technology"
                    onKeyPress={(e) => e.key === 'Enter' && addTechnology()}
                  />
                  <Button type="button" variant="outline" onClick={addTechnology}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="features" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Project Features</CardTitle>
              <CardDescription>
                Highlight the key features and capabilities of this project
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button type="button" variant="outline" onClick={addFeature}>
                <Plus className="mr-2 h-4 w-4" />
                Add Feature
              </Button>

              {features.map((feature, index) => (
                <Card key={feature.id} className="p-4">
                  <div className="flex items-start justify-between mb-4">
                    <h4 className="font-medium">Feature {index + 1}</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFeature(feature.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label>Number</Label>
                      <Input
                        value={feature.number}
                        onChange={(e) => updateFeature(feature.id, 'number', e.target.value)}
                        placeholder="01"
                      />
                    </div>
                    <div className="col-span-2 space-y-2">
                      <Label>Title</Label>
                      <Input
                        value={feature.title}
                        onChange={(e) => updateFeature(feature.id, 'title', e.target.value)}
                        placeholder="Feature title"
                      />
                    </div>
                  </div>
                  <div className="mt-4 space-y-2">
                    <Label>Description</Label>
                    <Textarea
                      value={feature.description}
                      onChange={(e) => updateFeature(feature.id, 'description', e.target.value)}
                      placeholder="Feature description"
                      rows={3}
                    />
                  </div>
                </Card>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="timeline" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Project Timeline</CardTitle>
              <CardDescription>
                Define the phases and milestones of your project
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button type="button" variant="outline" onClick={addTimelinePhase}>
                <Plus className="mr-2 h-4 w-4" />
                Add Phase
              </Button>

              {timeline.map((phase, index) => (
                <Card key={phase.id} className="p-4">
                  <div className="flex items-start justify-between mb-4">
                    <h4 className="font-medium">Phase {index + 1}</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeTimelinePhase(phase.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label>Phase Name</Label>
                      <Input
                        value={phase.phase}
                        onChange={(e) => updateTimelinePhase(phase.id, 'phase', e.target.value)}
                        placeholder="Phase 1"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Duration</Label>
                      <Input
                        value={phase.duration}
                        onChange={(e) => updateTimelinePhase(phase.id, 'duration', e.target.value)}
                        placeholder="8 months"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Title</Label>
                      <Input
                        value={phase.title}
                        onChange={(e) => updateTimelinePhase(phase.id, 'title', e.target.value)}
                        placeholder="Phase title"
                      />
                    </div>
                  </div>
                  <div className="mt-4 space-y-2">
                    <Label>Description</Label>
                    <Textarea
                      value={phase.description}
                      onChange={(e) => updateTimelinePhase(phase.id, 'description', e.target.value)}
                      placeholder="Phase description"
                      rows={3}
                    />
                  </div>
                </Card>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="seo" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>SEO Settings</CardTitle>
              <CardDescription>
                Optimize your project for search engines
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="seoTitle">SEO Title</Label>
                <Input
                  id="seoTitle"
                  value={seoTitle}
                  onChange={(e) => setSeoTitle(e.target.value)}
                  placeholder="SEO optimized title"
                />
                <p className="text-sm text-muted-foreground">
                  {seoTitle.length}/60 characters
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="seoDescription">SEO Description</Label>
                <Textarea
                  id="seoDescription"
                  value={seoDescription}
                  onChange={(e) => setSeoDescription(e.target.value)}
                  placeholder="SEO meta description"
                  rows={3}
                />
                <p className="text-sm text-muted-foreground">
                  {seoDescription.length}/160 characters
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}