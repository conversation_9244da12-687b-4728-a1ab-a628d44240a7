import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getProjectBySlug } from '@/lib/services/projects';
import { BlockRenderer } from '@/components/content-blocks';
import Layout from "@/components/layout/Layout";
import SectionHeader from "@/components/layout/SectionHeader";
import ProjectImageWithFallback from "@/components/ui/ProjectImageWithFallback";
import Link from 'next/link';
import './project-detail-enhanced.css';

interface ProjectPageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: ProjectPageProps): Promise<Metadata> {
  const project = await getProjectBySlug(params.slug);

  if (!project || project.status !== 'PUBLISHED') {
    return {
      title: 'Project Not Found',
    };
  }

  return {
    title: project.seoTitle || project.title,
    description: project.seoDescription || project.excerpt,
    openGraph: {
      title: project.seoTitle || project.title,
      description: project.seoDescription || project.excerpt,
      images: project.featuredImage ? [project.featuredImage] : [],
    },
  };
}

export default async function ProjectPage({ params }: ProjectPageProps) {
  const project = await getProjectBySlug(params.slug);

  if (!project || project.status !== 'PUBLISHED') {
    notFound();
  }

  return (
    <Layout>
      <SectionHeader
        title={project.title}
        group_page="Projects"
        current_page={project.title}
        display=""
      />

      {/* Project Overview Section */}
      <div className="sp">
        <div className="container">
          <div className="row">
            <div className="col-lg-8 m-auto">
              <div className="text-center">
                <div className="space30" />
                <p className="project-excerpt">{project.excerpt}</p>
                <div className="space30" />

                {/* Project Tags */}
                {project.tags && project.tags.length > 0 && (
                  <div className="project-tags">
                    {project.tags.map((tag) => (
                      <span key={tag} className="tag-item">
                        <i className="fa-solid fa-tag"></i>
                        {tag}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Project Meta Information */}
          <div className="space60" />
          <div className="row">
            <div className="col-lg-3 col-md-6">
              {/* <div className="project-meta-item text-center">
                <div className="icon">
                  <i className="fa-solid fa-building"></i>
                </div>
                <h4>Client</h4>
                <p>{project.clientName || 'Confidential'}</p>
              </div> */}
            </div>

            {/* <div className="col-lg-3 col-md-6">
              <div className="project-meta-item text-center">
                <div className="icon">
                  <i className="fa-solid fa-dollar-sign"></i>
                </div>
                <h4>Project Value</h4>
                <p>{project.projectValue || 'Confidential'}</p>
              </div>
            </div> */}
            {/* <div className="col-lg-3 col-md-6">
              <div className="project-meta-item text-center">
                <div className="icon">
                  <i className="fa-solid fa-calendar"></i>
                </div>
                <h4>Duration</h4>
                <p>
                  {project.startDate && project.endDate
                    ? `${new Date(project.startDate).getFullYear()} - ${new Date(project.endDate).getFullYear()}`
                    : 'Ongoing'
                  }
                </p>
              </div>
            </div> */}
          </div>
        </div>
      </div>

      {/* Content Blocks */}
      {project.contentBlocks && project.contentBlocks.length > 0 ? (
        <BlockRenderer blocks={project.contentBlocks} />
      ) : (
        /* Fallback Content */
        <>
          {/* Main Content Section */}
          <div className="sp">
            <div className="container">
              <div className="row">
                <div className="col-lg-8 m-auto">
                  <div className="service-details-area">
                    <article>
                      <div className="heading1">
                        {project.featuredImage && (
                          <>
                            <div className="image">
                              <img src={project.featuredImage} alt={project.title} />
                            </div>
                            <div className="space30" />
                          </>
                        )}
                        <h2>Project Overview</h2>
                        <div className="space16" />
                        <div dangerouslySetInnerHTML={{ __html: project.content || project.description || '' }} />
                      </div>
                    </article>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Features Section */}
          {project.features && project.features.length > 0 && (
            <div className="sp">
              <div className="container">
                <div className="row">
                  <div className="col-lg-6 m-auto text-center">
                    <div className="heading1">
                      <span className="span">Key Components</span>
                      <h2>Project Features</h2>
                      <div className="space16" />
                      <p>Discover the key features and components that make this project exceptional</p>
                    </div>
                  </div>
                </div>
                <div className="space60" />
                <div className="row">
                  {project.features.map((feature, index) => (
                    <div key={feature.id} className="col-lg-6 col-md-6">
                      <div className="feature-item">
                        <div className="feature-number">
                          <span>{feature.number || (index + 1).toString().padStart(2, '0')}</span>
                        </div>
                        <div className="feature-content">
                          <h3>{feature.title}</h3>
                          <div className="space16" />
                          <p>{feature.description}</p>
                        </div>
                      </div>
                      <div className="space30" />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Timeline Section */}
          {project.timeline && project.timeline.length > 0 && (
            <div className="sp">
              <div className="container">
                <div className="row">
                  <div className="col-lg-6 m-auto text-center">
                    <div className="heading1">
                      <span className="span">Project Journey</span>
                      <h2>Project Timeline</h2>
                      <div className="space16" />
                      <p>Follow the journey of this project from conception to completion</p>
                    </div>
                  </div>
                </div>
                <div className="space60" />
                {/* <div className="row">
                  <div className="col-lg-8 m-auto">
                    <div className="timeline-wrapper">
                      {project.timeline.map((phase, index) => (
                        <div key={phase.id} className="timeline-item">
                          <div className="timeline-phase">
                            <span className="phase-badge">{phase.phase}</span>
                            <span className="phase-duration">{phase.duration}</span>
                          </div>
                          <div className="timeline-content">
                            <h3>{phase.title}</h3>
                            <div className="space16" />
                            <p>{phase.description}</p>
                          </div>
                          {index < project.timeline.length - 1 && <div className="timeline-line"></div>}
                        </div>
                      ))}
                    </div>
                  </div>
                </div> */}
              </div>
            </div>
          )}

          {/* Technologies Section */}
          {project.technologies && project.technologies.length > 0 && (
            <div className="sp">
              <div className="container">
                <div className="row">
                  <div className="col-lg-6 m-auto text-center">
                    <div className="heading1">
                      <span className="span">Technology Stack</span>
                      <h2>Technologies Used</h2>
                      <div className="space16" />
                      <p>The cutting-edge technologies that powered this project</p>
                    </div>
                  </div>
                </div>
                <div className="space60" />
                <div className="row">
                  <div className="col-lg-8 m-auto">
                    <div className="technologies-grid">
                      {project.technologies.map((tech) => (
                        <span key={tech} className="tech-item">
                          <i className="fa-solid fa-code"></i>
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Gallery Section */}
          {project.gallery && project.gallery.length > 0 && (
            <div className="sp">
              <div className="container">
                <div className="row">
                  <div className="col-lg-6 m-auto text-center">
                    <div className="heading1">
                      <span className="span">Visual Journey</span>
                      <h2>Project Gallery</h2>
                      <div className="space16" />
                      <p>Explore the visual story of this project through our gallery</p>
                    </div>
                  </div>
                </div>
                <div className="space60" />
                <div className="row">
                  {project.gallery.map((image, index) => (
                    <div key={image.id} className="col-lg-4 col-md-6">
                      <div className="gallery-item">
                        <div className="image overlay-anim">
                          <ProjectImageWithFallback
                            src={image.src}
                            alt={image.alt}
                            fallbackSrc="/assets/img/placeholders/project-placeholder.svg"
                          />
                        </div>
                        {image.caption && (
                          <div className="gallery-caption">
                            <p>{image.caption}</p>
                          </div>
                        )}
                      </div>
                      <div className="space30" />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Stats Section */}
          {project.stats && project.stats.length > 0 && (
            <div className="sp">
              <div className="container">
                <div className="row">
                  <div className="col-lg-6 m-auto text-center">
                    <div className="heading1">
                      <span className="span">Measurable Results</span>
                      <h2>Project Impact</h2>
                      <div className="space16" />
                      <p>The quantifiable impact and results achieved through this project</p>
                    </div>
                  </div>
                </div>
                <div className="space60" />
                <div className="row">
                  {project.stats.map((stat, index) => (
                    <div key={index} className="col-lg-3 col-md-6">
                      <div className="stats-item text-center">
                        <div className="stats-number">
                          <span className="counter">{stat.number}</span>
                        </div>
                        <h4>{stat.label}</h4>
                      </div>
                      <div className="space30" />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {/* CTA Section */}
      <div className="sp" style={{backgroundColor: 'var(--vtc-primary-color)', color: 'white'}}>
        <div className="container">
          <div className="row">
            <div className="col-lg-8 m-auto text-center">
              <div className="heading1">
                <h2 style={{color: 'white'}}>Ready to Start Your Project?</h2>
                <div className="space16" />
                <p style={{color: 'rgba(255, 255, 255, 0.9)'}}>Let's discuss how we can help transform your infrastructure with innovative solutions.</p>
                <div className="space30" />
                <div className="cta-buttons">
                  <Link className="theme-btn1" href="/contact">
                    Get In Touch
                    <span>
                      <i className="fa-solid fa-arrow-right" />
                    </span>
                  </Link>
                  <Link className="theme-btn2" href="/projects">
                    View More Projects
                    <span>
                      <i className="fa-solid fa-arrow-right" />
                    </span>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}