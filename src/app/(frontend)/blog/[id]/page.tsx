import Layout from "@/components/layout/Layout";
import SectionHeader from "@/components/layout/SectionHeader";
import { BlogPostDetailComponent, BlogPostDetailSkeleton } from "@/components/blog/blog-post-detail";
import { BlogService } from "@/lib/services/blog.service";
import { Suspense } from "react";
import { notFound } from "next/navigation";
import type { Metadata } from "next";

interface BlogPostPageProps {
  params: { id: string }
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  try {
    const post = await BlogService.getPostBySlug(params.id)

    if (!post) {
      return {
        title: "Blog Post Not Found | Motshwanelo IT Consulting",
        description: "The requested blog post could not be found."
      }
    }

    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
    const postUrl = `${siteUrl}/blog/${post.slug}`

    return {
      title: `${post.seoTitle || post.title} | Motshwanelo IT Consulting`,
      description: post.seoDescription || post.excerpt || "Read this insightful blog post from Motshwanelo IT Consulting",
      keywords: post.seoKeywords || [post.category?.name, ...post.tags?.map(tag => tag.name) || []].filter(Boolean),
      authors: [{ name: post.author.name || 'Motshwanelo IT Consulting' }],
      openGraph: {
        title: post.seoTitle || post.title,
        description: post.seoDescription || post.excerpt || "Read this insightful blog post",
        type: "article",
        url: postUrl,
        siteName: 'Motshwanelo IT Consulting',
        images: post.featuredImage ? [
          {
            url: post.featuredImage.startsWith('http') ? post.featuredImage : `${siteUrl}${post.featuredImage}`,
            width: 1200,
            height: 630,
            alt: post.title,
          }
        ] : [
          {
            url: `${siteUrl}/assets/img/logo/logo.png`,
            width: 1200,
            height: 630,
            alt: 'Motshwanelo IT Consulting',
          }
        ],
        locale: 'en_US',
        publishedTime: post.publishedAt ? new Date(post.publishedAt).toISOString() : undefined,
        modifiedTime: post.updatedAt ? new Date(post.updatedAt).toISOString() : undefined,
        authors: [post.author.name || 'Motshwanelo IT Consulting'],
        section: post.category?.name,
        tags: post.tags?.map(tag => tag.name) || [],
      },
      twitter: {
        card: "summary_large_image",
        title: post.seoTitle || post.title,
        description: post.seoDescription || post.excerpt || "Read this insightful blog post",
        images: post.featuredImage ? [
          post.featuredImage.startsWith('http') ? post.featuredImage : `${siteUrl}${post.featuredImage}`
        ] : [`${siteUrl}/assets/img/logo/logo.png`],
        creator: '@motshwaneloitc',
        site: '@motshwaneloitc',
      },
      alternates: {
        canonical: postUrl,
      },
      robots: {
        index: post.status === 'PUBLISHED',
        follow: true,
        googleBot: {
          index: post.status === 'PUBLISHED',
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
    }
  } catch (error) {
    console.error('Error generating metadata:', error)
    return {
      title: "Blog Post | Motshwanelo IT Consulting",
      description: "Read our latest insights and expertise from Motshwanelo IT Consulting"
    }
  }
}

async function BlogPostContent({ slug }: { slug: string }) {
  try {
    // Validate slug format
    if (!slug || typeof slug !== 'string' || slug.trim() === '') {
      console.error('Invalid slug provided:', slug)
      notFound()
    }

    const post = await BlogService.getPostBySlug(slug.trim())

    if (!post) {
      console.log(`Blog post not found for slug: ${slug}`)
      notFound()
    }

    // Ensure post is published (unless in development)
    if (post.status !== 'PUBLISHED' && process.env.NODE_ENV === 'production') {
      console.log(`Blog post not published: ${slug}, status: ${post.status}`)
      notFound()
    }

    // Get related posts using the post ID with better error handling
    let relatedPosts: any[] = []
    try {
      relatedPosts = await BlogService.getRelatedPosts(post.id, 3)
    } catch (relatedError) {
      console.warn('Failed to load related posts:', relatedError)
      // Continue without related posts rather than failing the entire page
    }

    // Add related posts to the post object
    const postWithRelated = {
      ...post,
      relatedPosts: relatedPosts || []
    }

    return (
      <>
        {/* Include blog content CSS */}
        <link rel="stylesheet" href="/assets/css/blog-content.css" />

        <div className="blog-post-detail-section sp">
          <div className="container">
            <div className="row">
              <div className="col-lg-10 m-auto">
                <BlogPostDetailComponent post={postWithRelated} />
              </div>
            </div>
          </div>
        </div>
      </>
    )
  } catch (error) {
    console.error('Error loading blog post:', error)

    // Check if it's a database connection error or other server error
    if (error instanceof Error) {
      if (error.message.includes('connect') || error.message.includes('database')) {
        throw new Error('Database connection failed. Please try again later.')
      }
    }

    // For other errors, show 404
    notFound()
  }
}

export default function BlogPostPage({ params }: BlogPostPageProps) {
    return (
        <Layout>
            <SectionHeader
              title="Blog Article"
              group_page="Blog"
              current_page="Article"
              display="d-block"
            />

            <Suspense fallback={
              <div className="blog-post-skeleton-section sp">
                <div className="container">
                  <div className="row">
                    <div className="col-lg-10 m-auto">
                      <BlogPostDetailSkeleton />
                    </div>
                  </div>
                </div>
              </div>
            }>
              <BlogPostContent slug={params.id} />
            </Suspense>
        </Layout>
    );
}
