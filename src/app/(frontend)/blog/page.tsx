import Layout from "@/components/layout/Layout";
import SectionHeader from "@/components/layout/SectionHeader";
import { BlogService } from "@/lib/services/blog.service";
import { PostStatus } from "@prisma/client";
import type { Metadata } from "next";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "Industry Insights | Motshwanelo IT Consulting - Latest Technology Trends & News",
  description: "Stay updated with the latest IT trends, digital transformation insights, and technology news from Motshwanelo IT Consulting. Expert analysis on Smart Cities, Data Centres, and enterprise solutions.",
  keywords: ["IT Blog", "Technology News", "Digital Transformation", "Smart City Trends", "Data Centre News", "IT Industry Insights"],
  openGraph: {
    title: "Industry Insights | Latest Technology Trends & News",
    description: "Stay updated with expert insights on digital transformation, Smart Cities, Data Centres, and the latest IT industry trends.",
    type: "website",
    url: "https://motshwaneloitconsulting.co.za/blog",
  },
};

// Create a proper section component for blog content that matches frontend styling
function BlogSection({ postsResult }: {
  postsResult: any
}) {
  return (
    <div className="blog-page sp">
      <div className="container">
        <div className="row">
          <div className="col-lg-8 m-auto text-center">
            <div className="heading1">
              <span className="span" data-aos="zoom-in-left" data-aos-duration={700}>
                📚 Knowledge Hub
              </span>
              <h2 className="text-anime-style-3">Latest Industry Insights & Technology Trends</h2>
              <div className="space16" />
              <p data-aos="fade-up" data-aos-duration={800}>
                Stay ahead of the curve with expert insights on digital transformation, smart cities,
                data centre innovations, and the latest IT consulting trends shaping Africa's digital future.
              </p>
            </div>
          </div>
        </div>
        <div className="space60" />

        {/* Blog Posts Grid */}
        <div className="row">
          {postsResult.posts.map((post: any, index: number) => (
            <div key={post.id} className="col-lg-4 col-md-6">
              <div className="blog2-box" data-aos="fade-up" data-aos-duration={800} data-aos-delay={index * 100}>
                <div className="image">
                  <img
                    src={post.featuredImage || "/assets/img/blog/blog2-img1.png"}
                    alt={post.title}
                    style={{ width: '100%', height: '250px', objectFit: 'cover' }}
                  />
                </div>
                <div className="heading5">
                  <div className="tags">
                    <a href="#">
                      <img src="assets/img/icons/date2.png" alt="" />
                      {new Date(post.publishedAt).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </a>
                    <a href="#">
                      <img src="assets/img/icons/user2.png" alt="" /> {post.author.name}
                    </a>
                  </div>
                  <h4>
                    <a href={`/blog/${post.slug}`}>{post.title}</a>
                  </h4>
                  <div className="space16" />
                  <p>{post.excerpt}</p>
                  <div className="space16" />
                  <a href={`/blog/${post.slug}`} className="learn">
                    Read More
                    <span>
                      <i className="fa-solid fa-arrow-right" />
                    </span>
                  </a>
                </div>
              </div>
              <div className="space30" />
            </div>
          ))}
        </div>

        {/* Show More Button if there are more posts */}
        {postsResult.totalPages > 1 && (
          <div className="row">
            <div className="col-lg-12 text-center">
              <div className="space30" />
              <a href="/blog?page=2" className="theme-btn1">
                Load More Articles
                <span>
                  <i className="fa-solid fa-arrow-right" />
                </span>
              </a>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

async function BlogContent() {
  try {
    const postsResult = await BlogService.getPosts({
      page: 1,
      perPage: 12,
      status: PostStatus.PUBLISHED
    })

    return (
      <BlogSection
        postsResult={postsResult}
      />
    )
  } catch (error) {
    console.error('Error loading blog data:', error)
    return (
      <div className="blog-error-section sp">
        <div className="container">
          <div className="row">
            <div className="col-lg-8 m-auto text-center">
              <div className="heading1">
                <h2>Unable to load blog posts</h2>
                <div className="space16" />
                <p>We're having trouble loading the blog posts. Please try again later.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }
}

function BlogSkeleton() {
  return (
    <div className="blog-skeleton-section sp">
      <div className="container">
        <div className="row">
          <div className="col-lg-8 m-auto text-center">
            <div className="heading1">
              <div className="skeleton-title"></div>
            </div>
          </div>
        </div>
        <div className="space60" />
        <div className="row">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="col-lg-4 col-md-6">
              <div className="blog-skeleton-card">
                <div className="skeleton-image"></div>
                <div className="skeleton-content">
                  <div className="skeleton-line skeleton-line-title"></div>
                  <div className="skeleton-line skeleton-line-subtitle"></div>
                  <div className="skeleton-line skeleton-line-text"></div>
                  <div className="skeleton-line skeleton-line-text-short"></div>
                </div>
              </div>
              <div className="space30" />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default function Blog() {
    return (
        <Layout>
            {/* Include blog content CSS */}
            <link rel="stylesheet" href="/assets/css/blog-content.css" />

            <SectionHeader
              title="Industry Insights & News"
              group_page="Latest Technology Trends from Our Experts"
              current_page="Blog"
              display="d-block"
            />

            {/* Enhanced Blog Listing */}
            <Suspense fallback={<BlogSkeleton />}>
              <BlogContent />
            </Suspense>
        </Layout>
    );
}
